@echo off
echo ===================================
echo CS2 Skin Changer - Build Script
echo ===================================

REM Verificar se o CMake está instalado
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: CMake nao encontrado. Instale o CMake primeiro.
    pause
    exit /b 1
)

REM Criar diretório de build
if not exist "build" mkdir build
cd build

echo Configurando projeto com CMake...
cmake .. -A x64
if %errorlevel% neq 0 (
    echo ERRO: Falha na configuracao do CMake.
    pause
    exit /b 1
)

echo Compilando projeto...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao.
    pause
    exit /b 1
)

echo.
echo ===================================
echo Compilacao concluida com sucesso!
echo ===================================
echo.
echo Executavel criado em: build\bin\Release\CS2_SkinChanger.exe
echo.

REM Copiar executável para a raiz do projeto
if exist "bin\Release\CS2_SkinChanger.exe" (
    copy "bin\Release\CS2_SkinChanger.exe" "..\CS2_SkinChanger.exe" >nul
    echo Executavel copiado para a raiz do projeto.
)

echo Pressione qualquer tecla para continuar...
pause >nul
