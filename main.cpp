#include "memory.h"
#include "offsets.h"
#include "skins.h"
#include <thread>
#include <iostream>
#include <array>
#include <chrono>
#include <algorithm>

using namespace std;

// Funções de ofuscação (mantidas do código original)
int garb1(int x)
{
	int y = x;
	y = 10 - 10;
	x = y - 293;
	return x;
}

float randomFloat() {
	return (float)rand() / RAND_MAX;
}

int stringToInt(std::string str) {
	int result = 0;
	for (int i = 0; i < str.length(); i++) {
		result += (int)str[i];
	}
	return result;
}

int** addMatrices(int** matrix1, int** matrix2, int rows, int cols) {
	int** result = new int* [rows];
	for (int i = 0; i < rows; i++) {
		result[i] = new int[cols];
		for (int j = 0; j < cols; j++) {
			result[i][j] = matrix1[i][j] + matrix2[i][j];
		}
	}
	return result;
}

int factorial(int n) {
	if (n <= 1) {
		return 1;
	}
	return n * factorial(n - 1);
}

void ForceFullUpdate(const Memory& memory, uintptr_t engine)
{
	const auto networkGameClient = memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient);
	if (networkGameClient)
	{
		memory.Write<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick, -1);
		cout << "[DEBUG] Forced full update by setting delta tick to -1." << endl;
	}
}

void ForceViewModelUpdate(const Memory& memory, uintptr_t client, uintptr_t localPlayer)
{
	const auto viewModelServices = memory.Read<uintptr_t>(localPlayer + offset::m_pViewModelServices);
	if (!viewModelServices) return;

	const auto viewHandle = memory.Read<uintptr_t>(viewModelServices + offset::m_hViewModel);
	if (!viewHandle) return;

	const auto viewListEntry = memory.Read<uintptr_t>(
		client + offset::dwEntityList + (0x8 * ((viewHandle & 0x7FFF) >> 9)) + 16);
	if (!viewListEntry) return;

	const auto viewController = memory.Read<uintptr_t>(viewListEntry + 120 * (viewHandle & 0x1FF));
	if (!viewController) return;

	const auto viewNode = memory.Read<uintptr_t>(viewController + 0x318);
	if (!viewNode) return;

	const auto viewMask = memory.Read<uint64_t>(viewNode + 0x160);
	if (viewMask != 2)
	{
		memory.Write<uint64_t>(viewNode + 0x160, 2);
		cout << "[DEBUG] Forced ViewModel update." << endl;
	}
}

bool ApplySkinToWeapon(const Memory& memory, uintptr_t weapon, short itemDefIndex)
{
	const auto skinConfig = GetWeaponSkin(itemDefIndex);
	if (skinConfig.paintKit == 0) return false;

	cout << "[DEBUG] Applying skin to weapon ID " << itemDefIndex 
		 << " - Paint Kit: " << skinConfig.paintKit << endl;

	// Verificar se a skin já está aplicada
	const auto currentPaintKit = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit);
	if (currentPaintKit == skinConfig.paintKit) return false;

	// Aplicar configurações da skin
	memory.Write<int32_t>(weapon + offset::m_iItemIDHigh, -1);
	memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, skinConfig.paintKit);
	memory.Write<float>(weapon + offset::m_flFallbackWear, skinConfig.wear);
	memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, skinConfig.statTrak);
	memory.Write<int32_t>(weapon + offset::m_nFallbackSeed, skinConfig.seed);
	
	// Definir AccountID
	const auto originalOwner = memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow);
	memory.Write<int32_t>(weapon + offset::m_iAccountID, originalOwner);

	cout << "[DEBUG] Skin applied successfully!" << endl;
	return true;
}

int main()
{
	cout << "=== CS2 External Skin Changer ===" << endl;
	cout << "Desenvolvido com offsets atualizados para 2025-07-08" << endl;
	cout << "Pressione Enter para iniciar..." << endl;
	cin.get();

	cout << "[DEBUG] Inicializando memoria..." << endl;
	const auto memory = Memory("cs2.exe");

	cout << "[DEBUG] Obtendo enderecos dos modulos..." << endl;
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Falha ao obter endereco dos modulos." << endl;
		cout << "Certifique-se de que o CS2 esta rodando." << endl;
		cin.get();
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	cout << "[INFO] Skin Changer iniciado! Pressione Ctrl+C para parar." << endl;

	while (true)
	{
		try
		{
			const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
			if (!localPlayer)
			{
				cout << "[DEBUG] Local player not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Local player found: 0x" << hex << localPlayer << endl;

			const auto weaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
			if (!weaponServices)
			{
				cout << "[DEBUG] Weapon services not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Weapon services found: 0x" << hex << weaponServices << endl;

			// Ler armas do jogador - C_NetworkUtlVectorBase structure
			// Estrutura: size (0x0), data pointer (0x8)
			const auto weaponCount = memory.Read<int>(weaponServices + offset::m_hMyWeapons + 0x0); // size
			const auto weaponsArray = memory.Read<uintptr_t>(weaponServices + offset::m_hMyWeapons + 0x8); // data pointer

			cout << "[DEBUG] Weapon count: " << dec << weaponCount << endl;
			cout << "[DEBUG] Weapons array: 0x" << hex << weaponsArray << endl;

			if (!weaponsArray || weaponCount <= 0 || weaponCount > 64)
			{
				cout << "[DEBUG] Invalid weapons data, skipping..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			array<uintptr_t, 64> weapons{};
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				weapons[i] = memory.Read<uintptr_t>(weaponsArray + (i * 0x4));
			}

			bool needsUpdate = false;
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				const auto handle = weapons[i];
				if (!handle) continue;

				cout << "[DEBUG] Processing weapon handle: 0x" << hex << handle << endl;

				// Calcular endereco da arma usando a nova estrutura de entity list
				const auto listEntry = (handle & 0x7FFF) >> 9;
				const auto listIndex = handle & 0x1FF;

				const auto weaponListEntry = memory.Read<uintptr_t>(
					client + offset::dwEntityList + (0x8 * listEntry) + 0x10);
				if (!weaponListEntry)
				{
					cout << "[DEBUG] Invalid weapon list entry for handle: 0x" << hex << handle << endl;
					continue;
				}

				const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 0x78 * listIndex);
				if (!weapon)
				{
					cout << "[DEBUG] Invalid weapon entity for handle: 0x" << hex << handle << endl;
					continue;
				}

				cout << "[DEBUG] Weapon entity: 0x" << hex << weapon << endl;

				// Método correto: ler do player -> clipping weapon -> AttributeManager -> Item -> ItemDefinitionIndex
				const auto clippingWeapon = memory.Read<uintptr_t>(localPlayer + offset::m_pClippingWeapon);
				if (!clippingWeapon)
				{
					cout << "[DEBUG] Invalid clipping weapon" << endl;
					continue;
				}

				cout << "[DEBUG] Clipping weapon: 0x" << hex << clippingWeapon << endl;

				// Ler AttributeManager da arma
				const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
				if (!attributeManager)
				{
					cout << "[DEBUG] Invalid attribute manager" << endl;
					continue;
				}

				cout << "[DEBUG] Attribute manager: 0x" << hex << attributeManager << endl;

				// Ler Item do AttributeManager
				const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
				if (!item)
				{
					cout << "[DEBUG] Invalid item" << endl;
					continue;
				}

				cout << "[DEBUG] Item: 0x" << hex << item << endl;

				// Finalmente ler o ItemDefinitionIndex
				const auto itemDefIndex = memory.Read<uint16_t>(item + offset::m_iItemDefinitionIndex);
				cout << "[DEBUG] Item definition index: " << dec << itemDefIndex << endl;

				if (itemDefIndex > 0 && itemDefIndex < 1000) // Validar range
				{
					if (ApplySkinToWeapon(memory, weapon, itemDefIndex))
					{
						needsUpdate = true;
					}
				}
				else
				{
					cout << "[DEBUG] Invalid item definition index: " << itemDefIndex << endl;
				}
			}

			// Forcar atualizacao se necessario
			if (needsUpdate)
			{
				ForceViewModelUpdate(memory, client, localPlayer);
				ForceFullUpdate(memory, engine);
			}

			this_thread::sleep_for(chrono::milliseconds(500));
		}
		catch (const exception& e)
		{
			cout << "[ERROR] Excecao capturada: " << e.what() << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
		}
	}

	return 0;
}
