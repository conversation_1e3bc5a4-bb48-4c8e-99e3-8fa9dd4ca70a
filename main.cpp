#include "memory.h"
#include "offsets.h"
#include "skins.h"
#include <thread>
#include <iostream>
#include <array>
#include <chrono>
#include <algorithm>

using namespace std;

// Funções de ofuscação (mantidas do código original)
int garb1(int x)
{
	int y = x;
	y = 10 - 10;
	x = y - 293;
	return x;
}

float randomFloat() {
	return (float)rand() / RAND_MAX;
}

int stringToInt(std::string str) {
	int result = 0;
	for (int i = 0; i < str.length(); i++) {
		result += (int)str[i];
	}
	return result;
}

int** addMatrices(int** matrix1, int** matrix2, int rows, int cols) {
	int** result = new int* [rows];
	for (int i = 0; i < rows; i++) {
		result[i] = new int[cols];
		for (int j = 0; j < cols; j++) {
			result[i][j] = matrix1[i][j] + matrix2[i][j];
		}
	}
	return result;
}

int factorial(int n) {
	if (n <= 1) {
		return 1;
	}
	return n * factorial(n - 1);
}

std::string GetWeaponName(const Memory& memory, uintptr_t weapon)
{
	try
	{
		// Ler EntityIdentity (weapon + 0x10)
		const auto entityIdentity = memory.Read<uintptr_t>(weapon + 0x10);
		if (!entityIdentity || entityIdentity < 0x1000) return "invalid";

		// Ler DesignerName (EntityIdentity + 0x20)
		const auto designerNamePtr = memory.Read<uintptr_t>(entityIdentity + 0x20);
		if (!designerNamePtr || designerNamePtr < 0x1000) return "invalid";

		// Ler string do nome da arma
		char weaponName[64] = { 0 };
		for (int i = 0; i < 63; i++)
		{
			char c = memory.Read<char>(designerNamePtr + i);
			if (c == 0) break;
			weaponName[i] = c;
		}

		std::string name(weaponName);

		// Verificar se contém "weapon_"
		if (name.find("weapon_") == std::string::npos) return "invalid";

		// Remover prefixo "weapon_"
		return name.substr(7);
	}
	catch (...)
	{
		return "error";
	}
}

void ForceFullUpdate(const Memory& memory, uintptr_t engine)
{
	const auto networkGameClient = memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient);
	cout << "[DEBUG] NetworkGameClient: 0x" << hex << networkGameClient << endl;

	if (networkGameClient && networkGameClient > 0x1000)
	{
		const auto deltaTick = memory.Read<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick);
		cout << "[DEBUG] Current delta tick: " << dec << deltaTick << endl;

		memory.Write<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick, -1);
		cout << "[DEBUG] Forced full update by setting delta tick to -1." << endl;
	}
	else
	{
		cout << "[ERROR] Invalid NetworkGameClient address" << endl;
	}
}

void ForceViewModelUpdate(const Memory& memory, uintptr_t client, uintptr_t localPlayer)
{
	const auto viewModelServices = memory.Read<uintptr_t>(localPlayer + offset::m_pViewModelServices);
	if (!viewModelServices) return;

	const auto viewHandle = memory.Read<uintptr_t>(viewModelServices + offset::m_hViewModel);
	if (!viewHandle) return;

	const auto viewListEntry = memory.Read<uintptr_t>(
		client + offset::dwEntityList + (0x8 * ((viewHandle & 0x7FFF) >> 9)) + 16);
	if (!viewListEntry) return;

	const auto viewController = memory.Read<uintptr_t>(viewListEntry + 120 * (viewHandle & 0x1FF));
	if (!viewController) return;

	const auto viewNode = memory.Read<uintptr_t>(viewController + 0x318);
	if (!viewNode) return;

	const auto viewMask = memory.Read<uint64_t>(viewNode + 0x160);
	if (viewMask != 2)
	{
		memory.Write<uint64_t>(viewNode + 0x160, 2);
		cout << "[DEBUG] Forced ViewModel update." << endl;
	}
}

bool ApplySkinToWeapon(const Memory& memory, uintptr_t weapon, short itemDefIndex)
{
	const auto skinConfig = GetWeaponSkin(itemDefIndex);
	if (skinConfig.paintKit == 0)
	{
		cout << "[DEBUG] No skin configuration found for weapon ID: " << itemDefIndex << endl;
		return false;
	}

	cout << "[DEBUG] Applying skin to weapon ID " << itemDefIndex
		 << " - Paint Kit: " << skinConfig.paintKit << endl;

	// Verificar se a skin já está aplicada
	const auto currentPaintKit = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit);
	if (currentPaintKit == skinConfig.paintKit)
	{
		cout << "[DEBUG] Skin already applied (Paint Kit: " << currentPaintKit << ")" << endl;
		return false;
	}

	cout << "[DEBUG] Current paint kit: " << currentPaintKit << ", applying new: " << skinConfig.paintKit << endl;

	// Aplicar configurações da skin usando offsets corretos
	try
	{
		// Ler AttributeManager para aplicar as skins corretamente
		const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
		if (attributeManager && attributeManager > 0x1000)
		{
			const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
			if (item && item > 0x1000)
			{
				// Aplicar via AttributeManager -> Item
				memory.Write<int32_t>(item + offset::m_iItemIDHigh, -1);
				memory.Write<int32_t>(item + offset::m_iAccountID, memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow));
			}
		}

		// Aplicar fallback values diretamente na arma (C_EconEntity)
		memory.Write<int32_t>(weapon + offset::m_iItemIDHigh, -1);
		memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, skinConfig.paintKit);
		memory.Write<float>(weapon + offset::m_flFallbackWear, skinConfig.wear);
		memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, skinConfig.statTrak);
		memory.Write<int32_t>(weapon + offset::m_nFallbackSeed, skinConfig.seed);

		// Definir AccountID
		const auto originalOwner = memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow);
		memory.Write<int32_t>(weapon + offset::m_iAccountID, originalOwner);

		cout << "[DEBUG] Skin configuration applied successfully!" << endl;
		return true;
	}
	catch (...)
	{
		cout << "[ERROR] Exception occurred while applying skin" << endl;
		return false;
	}
}

int main()
{
	cout << "=== CS2 External Skin Changer ===" << endl;
	cout << "Desenvolvido com offsets atualizados para 2025-07-08" << endl;
	cout << "Pressione Enter para iniciar..." << endl;
	cin.get();

	cout << "[DEBUG] Inicializando memoria..." << endl;
	const auto memory = Memory("cs2.exe");

	cout << "[DEBUG] Obtendo enderecos dos modulos..." << endl;
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Falha ao obter endereco dos modulos." << endl;
		cout << "Certifique-se de que o CS2 esta rodando." << endl;
		cin.get();
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	cout << "[INFO] Skin Changer iniciado! Pressione Ctrl+C para parar." << endl;

	while (true)
	{
		try
		{
			const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
			if (!localPlayer)
			{
				cout << "[DEBUG] Local player not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Local player found: 0x" << hex << localPlayer << endl;

			const auto weaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
			if (!weaponServices)
			{
				cout << "[DEBUG] Weapon services not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Weapon services found: 0x" << hex << weaponServices << endl;

			// Ler armas do jogador - C_NetworkUtlVectorBase structure
			// Estrutura: size (0x0), data pointer (0x8)
			const auto weaponCount = memory.Read<int>(weaponServices + offset::m_hMyWeapons + 0x0); // size
			const auto weaponsArray = memory.Read<uintptr_t>(weaponServices + offset::m_hMyWeapons + 0x8); // data pointer

			cout << "[DEBUG] Weapon count: " << dec << weaponCount << endl;
			cout << "[DEBUG] Weapons array: 0x" << hex << weaponsArray << endl;

			if (!weaponsArray || weaponCount <= 0 || weaponCount > 64)
			{
				cout << "[DEBUG] Invalid weapons data, skipping..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			array<uintptr_t, 64> weapons{};
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				weapons[i] = memory.Read<uintptr_t>(weaponsArray + (i * 0x4));
			}

			bool needsUpdate = false;
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				const auto handle = weapons[i];
				if (!handle) continue;

				cout << "[DEBUG] Processing weapon handle: 0x" << hex << handle << endl;

				// Calcular endereco da arma usando a nova estrutura de entity list
				const auto listEntry = (handle & 0x7FFF) >> 9;
				const auto listIndex = handle & 0x1FF;

				const auto weaponListEntry = memory.Read<uintptr_t>(
					client + offset::dwEntityList + (0x8 * listEntry) + 0x10);
				if (!weaponListEntry)
				{
					cout << "[DEBUG] Invalid weapon list entry for handle: 0x" << hex << handle << endl;
					continue;
				}

				const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 0x78 * listIndex);
				if (!weapon)
				{
					cout << "[DEBUG] Invalid weapon entity for handle: 0x" << hex << handle << endl;
					continue;
				}

				cout << "[DEBUG] Weapon entity: 0x" << hex << weapon << endl;

				// Validar se o endereço da arma é válido
				if (weapon < 0x1000 || weapon > 0x7FFFFFFFFFFF)
				{
					cout << "[DEBUG] Invalid weapon address: 0x" << hex << weapon << endl;
					continue;
				}

				// Método 1: Tentar ler diretamente do AttributeManager da arma
				const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
				cout << "[DEBUG] AttributeManager: 0x" << hex << attributeManager << endl;

				uint16_t itemDefIndex = 0;
				bool foundValidIndex = false;

				if (attributeManager && attributeManager > 0x1000 && attributeManager < 0x7FFFFFFFFFFF)
				{
					const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
					cout << "[DEBUG] Item: 0x" << hex << item << endl;

					if (item && item > 0x1000 && item < 0x7FFFFFFFFFFF)
					{
						itemDefIndex = memory.Read<uint16_t>(item + offset::m_iItemDefinitionIndex);
						cout << "[DEBUG] Method 1 - Item definition index: " << dec << itemDefIndex << endl;

						if (itemDefIndex > 0 && itemDefIndex < 1000)
						{
							foundValidIndex = true;
						}
					}
				}

				// Método 2: Se o método 1 falhou, tentar ler diretamente da arma (fallback)
				if (!foundValidIndex)
				{
					itemDefIndex = memory.Read<uint16_t>(weapon + offset::m_iItemDefinitionIndex);
					cout << "[DEBUG] Method 2 - Item definition index: " << dec << itemDefIndex << endl;

					if (itemDefIndex > 0 && itemDefIndex < 1000)
					{
						foundValidIndex = true;
					}
				}

				// Método 3: Tentar diferentes offsets conhecidos (último recurso)
				if (!foundValidIndex)
				{
					// Tentar offset alternativo baseado em C_EconItemView diretamente
					const auto altItemDefIndex = memory.Read<uint16_t>(weapon + 0x1148 + 0x50 + 0x1BA);
					cout << "[DEBUG] Method 3 - Alternative item definition index: " << dec << altItemDefIndex << endl;

					if (altItemDefIndex > 0 && altItemDefIndex < 1000)
					{
						itemDefIndex = altItemDefIndex;
						foundValidIndex = true;
					}
				}
				// Processar apenas se encontramos um índice válido
				if (foundValidIndex)
				{
					const auto weaponName = GetWeaponName(memory, weapon);
					cout << "[DEBUG] Valid weapon found - ID: " << dec << itemDefIndex
						 << ", Name: " << weaponName << endl;

					if (ApplySkinToWeapon(memory, weapon, itemDefIndex))
					{
						needsUpdate = true;
						cout << "[DEBUG] Skin applied successfully to " << weaponName
							 << " (ID: " << itemDefIndex << ")" << endl;
					}
				}
				else
				{
					const auto weaponName = GetWeaponName(memory, weapon);
					cout << "[DEBUG] No valid item definition index found for weapon: "
						 << weaponName << " (0x" << hex << weapon << ")" << endl;
				}
			}

			// Forcar atualizacao se necessario
			if (needsUpdate)
			{
				ForceViewModelUpdate(memory, client, localPlayer);
				ForceFullUpdate(memory, engine);
			}

			this_thread::sleep_for(chrono::milliseconds(500));
		}
		catch (const exception& e)
		{
			cout << "[ERROR] Excecao capturada: " << e.what() << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
		}
	}

	return 0;
}
