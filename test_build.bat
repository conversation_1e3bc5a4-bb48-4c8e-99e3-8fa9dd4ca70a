@echo off
echo ===================================
echo CS2 Skin Changer - Test Build
echo ===================================

REM Verificar se os arquivos necessários existem
if not exist "main.cpp" (
    echo ERRO: main.cpp nao encontrado
    pause
    exit /b 1
)

if not exist "memory.h" (
    echo ERRO: memory.h nao encontrado
    pause
    exit /b 1
)

if not exist "offsets.h" (
    echo ERRO: offsets.h nao encontrado
    pause
    exit /b 1
)

if not exist "skins.h" (
    echo ERRO: skins.h nao encontrado
    pause
    exit /b 1
)

echo Todos os arquivos necessarios encontrados!
echo.

REM Verificar se o CMake está instalado
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo AVISO: CMake nao encontrado. Tentando compilacao direta...
    echo.
    
    REM Tentar compilação direta com cl.exe (Visual Studio)
    cl.exe >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERRO: Nem CMake nem Visual Studio encontrados.
        echo Instale o Visual Studio ou CMake primeiro.
        pause
        exit /b 1
    )
    
    echo Compilando com Visual Studio...
    cl.exe /EHsc /std:c++17 main.cpp /Fe:CS2_SkinChanger.exe
    if %errorlevel% neq 0 (
        echo ERRO: Falha na compilacao.
        pause
        exit /b 1
    )
    
    echo Compilacao concluida com sucesso!
    echo Executavel: CS2_SkinChanger.exe
    pause
    exit /b 0
)

echo CMake encontrado! Usando build normal...
call build.bat
