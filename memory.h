#pragma once
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <Windows.h>
#include <TlHelp32.h>

#include <cstdint>
#include <string_view>

class Memory
{
private:
	std::uintptr_t processId = 0;
	void* processHandle = nullptr;

public:

	Memory(const std::string_view processName) noexcept
	{
		::PROCESSENTRY32W entry = { };
		entry.dwSize = sizeof(::PROCESSENTRY32W);

		const auto snapShot = ::CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);

		while (::Process32NextW(snapShot, &entry))
		{
			// Converter WCHAR para char
			char exeFileA[MAX_PATH];
			::WideCharToMultiByte(CP_ACP, 0, entry.szExeFile, -1, exeFileA, MAX_PATH, NULL, NULL);

			if (!processName.compare(exeFileA))
			{
				processId = entry.th32ProcessID;
				processHandle = ::OpenProcess(PROCESS_ALL_ACCESS, FALSE, static_cast<DWORD>(processId));
				break;
			}
		}

		if (snapShot)
			::CloseHandle(snapShot);
	}

	~Memory()
	{
		if (processHandle)
			::CloseHandle(processHandle);
	}

	const std::uintptr_t GetModuleAddress(const std::string_view moduleName) const noexcept
	{
		::MODULEENTRY32W entry = { };
		entry.dwSize = sizeof(::MODULEENTRY32W);

		const auto snapShot = ::CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, static_cast<DWORD>(processId));

		std::uintptr_t result = 0;

		while (::Module32NextW(snapShot, &entry))
		{
			// Converter WCHAR para char
			char moduleNameA[MAX_PATH];
			::WideCharToMultiByte(CP_ACP, 0, entry.szModule, -1, moduleNameA, MAX_PATH, NULL, NULL);

			if (!moduleName.compare(moduleNameA))
			{
				result = reinterpret_cast<std::uintptr_t>(entry.modBaseAddr);
				break;
			}
		}

		if (snapShot)
			::CloseHandle(snapShot);

		return result;
	}

	template <typename T>
	constexpr const T Read(const std::uintptr_t& address) const noexcept
	{
		T value = { };
		::ReadProcessMemory(processHandle, reinterpret_cast<const void*>(address), &value, sizeof(T), NULL);
		return value;
	}

	template <typename T>
	constexpr void Write(const std::uintptr_t& address, const T& value) const noexcept
	{
		::WriteProcessMemory(processHandle, reinterpret_cast<void*>(address), &value, sizeof(T), NULL);
	}
};
