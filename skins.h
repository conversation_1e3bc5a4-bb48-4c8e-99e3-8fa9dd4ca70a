#pragma once
#include "weapon_ids.h"

// Definições de skins para diferentes armas
// Baseado nos IDs de definição de item do CS2

struct SkinConfig {
    int paintKit;
    float wear;
    int statTrak;
    int seed;
};

// Função para obter configuração de skin baseada no item definition index
constexpr SkinConfig GetWeaponSkin(const short& itemDefinitionIndex)
{
    switch (itemDefinitionIndex)
    {
        // AK-47 (ID: 7)
        case 7:
        case WeaponIDs::AK47:
            return { PopularSkins::AK47_REDLINE, 0.1f, 6969, 1 }; // Redline

        // AWP (ID: 9)
        case 9:
        case WeaponIDs::AWP:
            return { PopularSkins::AWP_DRAGON_LORE, 0.1f, 6969, 1 }; // Dragon Lore

        // Glock-18 (ID: 4)
        case 4:
        case WeaponIDs::GLOCK:
            return { PopularSkins::GLOCK_WATER_ELEMENTAL, 0.1f, 6969, 1 }; // Water Elemental

        // USP-S (ID: 61)
        case 61:
        case WeaponIDs::USP:
            return { PopularSkins::USPS_KILL_CONFIRMED, 0.1f, 6969, 1 }; // Kill Confirmed

        // M4A4 (ID: 16)
        case 16:
        case WeaponIDs::M4A4:
            return { PopularSkins::M4A4_HOWL, 0.1f, 6969, 1 }; // Howl

        // M4A1-S (ID: 60)
        case 60:
        case WeaponIDs::M4A1:
            return { PopularSkins::M4A1S_HYPER_BEAST, 0.1f, 6969, 1 }; // Hyper Beast

        // Desert Eagle (ID: 1)
        case 1:
        case WeaponIDs::DEAGLE:
            return { PopularSkins::DEAGLE_BLAZE, 0.1f, 6969, 1 }; // Blaze
        
        // P250
        case WeaponIDs::P250:
            return { PopularSkins::P250_ASIIMOV, 0.1f, 6969, 1 }; // Asiimov

        // Famas
        case WeaponIDs::FAMAS:
            return { 155, 0.1f, 6969, 1 }; // Afterimage

        // Galil AR
        case WeaponIDs::GALIL:
            return { 198, 0.1f, 6969, 1 }; // Chatterbox

        // P90
        case WeaponIDs::P90:
            return { 159, 0.1f, 6969, 1 }; // Asiimov

        // MP7
        case WeaponIDs::MP7:
            return { 102, 0.1f, 6969, 1 }; // Nemesis

        // UMP-45
        case WeaponIDs::UMP45:
            return { 286, 0.1f, 6969, 1 }; // Primal Saber
        
        // XM1014 (ID: 25)
        case 25: 
            return { 93, 0.1f, 6969, 1 }; // Tranquility
        
        // Nova (ID: 35)
        case 35: 
            return { 176, 0.1f, 6969, 1 }; // Antique
        
        // Negev (ID: 28)
        case 28: 
            return { 190, 0.1f, 6969, 1 }; // Loudmouth
        
        // M249 (ID: 14)
        case 14: 
            return { 131, 0.1f, 6969, 1 }; // System Lock
        
        // MAC-10 (ID: 17)
        case 17: 
            return { 188, 0.1f, 6969, 1 }; // Neon Rider
        
        // MP9 (ID: 18)
        case 18: 
            return { 177, 0.1f, 6969, 1 }; // Hypnotic
        
        // MP5-SD (ID: 23)
        case 23: 
            return { 800, 0.1f, 6969, 1 }; // Phosphor
        
        // Tec-9 (ID: 30)
        case 30: 
            return { 274, 0.1f, 6969, 1 }; // Fuel Injector
        
        // Five-SeveN (ID: 3)
        case 3: 
            return { 143, 0.1f, 6969, 1 }; // Case Hardened
        
        // CZ75-Auto (ID: 63)
        case 63: 
            return { 220, 0.1f, 6969, 1 }; // The Fuschia Is Now
        
        // Dual Berettas (ID: 2)
        case 2: 
            return { 156, 0.1f, 6969, 1 }; // Hemoglobin
        
        // R8 Revolver (ID: 64)
        case 64: 
            return { 415, 0.1f, 6969, 1 }; // Fade
        
        // SSG 08 (ID: 40)
        case 40: 
            return { 179, 0.1f, 6969, 1 }; // Blood in the Water
        
        // SG 553 (ID: 39)
        case 39: 
            return { 151, 0.1f, 6969, 1 }; // Cyrex
        
        // AUG (ID: 8)
        case 8: 
            return { 455, 0.1f, 6969, 1 }; // Akihabara Accept
        
        // SCAR-20 (ID: 38)
        case 38: 
            return { 175, 0.1f, 6969, 1 }; // Cardiac
        
        // G3SG1 (ID: 11)
        case 11: 
            return { 128, 0.1f, 6969, 1 }; // Chronos
        
        // PP-Bizon (ID: 26)
        case 26: 
            return { 190, 0.1f, 6969, 1 }; // Judgement of Anubis
        
        // MAG-7 (ID: 27)
        case 27: 
            return { 165, 0.1f, 6969, 1 }; // Bulldozer
        
        // Sawed-Off (ID: 29)
        case 29:
            return { 168, 0.1f, 6969, 1 }; // The Kraken

        // Knife (ID: 42)
        case 42:
            return { 38, 0.1f, 6969, 1 }; // Fade

        // Knife T (ID: 59)
        case 59:
            return { 38, 0.1f, 6969, 1 }; // Fade

        // HE Grenade (ID: 44)
        case 44:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // Flashbang (ID: 43)
        case 43:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // Smoke Grenade (ID: 45)
        case 45:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // Incendiary Grenade (ID: 46)
        case 46:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // Decoy (ID: 47)
        case 47:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // Molotov (ID: 48)
        case 48:
            return { 0, 0.0f, 0, 0 }; // Sem skin para granadas

        // C4 (ID: 49)
        case 49:
            return { 0, 0.0f, 0, 0 }; // Sem skin para C4

        // Defuse Kit (ID: 55)
        case 55:
            return { 0, 0.0f, 0, 0 }; // Sem skin para kit

        // Kevlar (ID: 50)
        case 50:
            return { 0, 0.0f, 0, 0 }; // Sem skin para kevlar

        // Helmet (ID: 51)
        case 51:
            return { 0, 0.0f, 0, 0 }; // Sem skin para helmet

        // Zeus x27 (ID: 31)
        case 31:
            return { 519, 0.1f, 6969, 1 }; // Olympus

        // ID 71 (possivelmente grenade ou item especial)
        case 71:
            return { 0, 0.0f, 0, 0 }; // Sem skin

        // ID 322 (possivelmente knife ou item especial)
        case 322:
            return { 38, 0.1f, 6969, 1 }; // Fade (se for faca)

        default:
            return { 0, 0.0f, 0, 0 }; // Sem skin
    }
}
