{"animationsystem.dll": {"classes": {"AimCameraOpFixedSettings_t": {"fields": {"m_nCameraJointIndex": 4, "m_nChainIndex": 0, "m_nClavicleLeftJointIndex": 12, "m_nClavicleRightJointIndex": 16, "m_nDepenetrationJointIndex": 20, "m_nPelvisJointIndex": 8, "m_propJoints": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "AimMatrixOpFixedSettings_t": {"fields": {"m_attachment": 0, "m_bTargetIsPosition": 204, "m_bUseBiasAndClamp": 205, "m_biasAndClampBlendCurve": 216, "m_damping": 128, "m_eBlendMode": 184, "m_flBiasAndClampPitchOffset": 212, "m_flBiasAndClampYawOffset": 208, "m_flMaxPitchAngle": 192, "m_flMaxYawAngle": 188, "m_nBoneMaskIndex": 200, "m_nSequenceMaxFrame": 196, "m_poseCacheHandles": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "AnimComponentID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimNodeID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimNodeOutputID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimParamID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimScriptHandle": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimStateID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimTagID": {"fields": {"m_id": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "AnimationDecodeDebugDumpElement_t": {"fields": {"m_decodeOps": 40, "m_decodedAnims": 88, "m_internalOps": 64, "m_modelName": 8, "m_nEntityIndex": 0, "m_poseParams": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "AnimationDecodeDebugDump_t": {"fields": {"m_elems": 8, "m_processingType": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "AnimationSnapshotBase_t": {"fields": {"m_DecodeDump": 152, "m_SnapshotType": 144, "m_bBonesInWorldSpace": 64, "m_bHasDecodeDump": 148, "m_boneSetupMask": 72, "m_boneTransforms": 96, "m_flRealTime": 0, "m_flexControllers": 120, "m_rootToWorld": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "AnimationSnapshot_t": {"fields": {"m_modelName": 280, "m_nEntIndex": 272}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "AnimationSnapshotBase_t"}, "AttachmentHandle_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "BlendItem_t": {"fields": {"m_bUseCustomDuration": 56, "m_flDuration": 52, "m_hSequence": 40, "m_pChild": 24, "m_tags": 0, "m_vPos": 44}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "BoneDemoCaptureSettings_t": {"fields": {"m_boneName": 0, "m_flErrorQuantizationRotationMax": 20, "m_flErrorQuantizationScaleMax": 28, "m_flErrorQuantizationTranslationMax": 24, "m_flErrorSplineRotationMax": 8, "m_flErrorSplineScaleMax": 16, "m_flErrorSplineTranslationMax": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CActionComponentUpdater": {"fields": {"m_actions": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CAddUpdateNode": {"fields": {"m_bApplyChannelsSeparately": 145, "m_bApplyScale": 147, "m_bApplyToFootMotion": 144, "m_bUseModelSpace": 146, "m_footMotionTiming": 140}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBinaryUpdateNode"}, "CAimCameraUpdateNode": {"fields": {"m_hParameterCameraClearanceDistance": 120, "m_hParameterCameraOnly": 114, "m_hParameterOrientation": 106, "m_hParameterPelvisOffset": 110, "m_hParameterPosition": 104, "m_hParameterSpineRotationWeight": 108, "m_hParameterUseIK": 112, "m_hParameterWeaponDepenetrationDelta": 118, "m_hParameterWeaponDepenetrationDistance": 116, "m_opFixedSettings": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CAimConstraint": {"fields": {"m_nUpType": 128, "m_qAimOffset": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CAimMatrixUpdateNode": {"fields": {"m_bLockWhenWaning": 357, "m_bResetChild": 356, "m_hSequence": 352, "m_opFixedSettings": 112, "m_paramIndex": 348, "m_target": 344}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CAnimActionUpdater": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimActivity": {"fields": {"m_nActivity": 16, "m_nFlags": 20, "m_nWeight": 24, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimAttachment": {"fields": {"m_influenceIndices": 96, "m_influenceOffsets": 48, "m_influenceRotations": 0, "m_influenceWeights": 108, "m_numInfluences": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimBone": {"fields": {"m_flags": 68, "m_name": 0, "m_parent": 16, "m_pos": 20, "m_qAlignment": 52, "m_quat": 32, "m_scale": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimBoneDifference": {"fields": {"m_bHasMovement": 45, "m_bHasRotation": 44, "m_name": 0, "m_parent": 16, "m_posError": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimComponentUpdater": {"fields": {"m_bStartEnabled": 40, "m_id": 32, "m_name": 24, "m_networkMode": 36}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimCycle": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CCycleBase"}, "CAnimData": {"fields": {"m_animArray": 32, "m_decoderArray": 56, "m_nMaxUniqueFrameIndex": 80, "m_name": 16, "m_segmentArray": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimDataChannelDesc": {"fields": {"m_nElementIndexArray": 96, "m_nElementMaskArray": 120, "m_nFlags": 32, "m_nType": 36, "m_szChannelClass": 0, "m_szDescription": 56, "m_szElementNameArray": 72, "m_szGrouping": 40, "m_szVariableName": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimDecoder": {"fields": {"m_nType": 20, "m_nVersion": 16, "m_szName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimDemoCaptureSettings": {"fields": {"m_baseSequence": 64, "m_boneSelectionMode": 76, "m_bones": 80, "m_flIkRotation_MaxQuantizationError": 56, "m_flIkRotation_MaxSplineError": 24, "m_flIkTranslation_MaxQuantizationError": 60, "m_flIkTranslation_MaxSplineError": 28, "m_ikChains": 104, "m_nBaseSequenceFrame": 72, "m_vecErrorRangeQuantizationRotation": 32, "m_vecErrorRangeQuantizationScale": 48, "m_vecErrorRangeQuantizationTranslation": 40, "m_vecErrorRangeSplineRotation": 0, "m_vecErrorRangeSplineScale": 16, "m_vecErrorRangeSplineTranslation": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimDesc": {"fields": {"fps": 24, "framestalltime": 376, "m_Data": 32, "m_activityArray": 328, "m_eventArray": 304, "m_flags": 16, "m_hierarchyArray": 352, "m_movementArray": 248, "m_name": 0, "m_sequenceParams": 456, "m_vecBoneWorldMax": 432, "m_vecBoneWorldMin": 408, "m_vecRootMax": 392, "m_vecRootMin": 380, "m_xInitialOffset": 272}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimDesc_Flag": {"fields": {"m_bAllZeros": 1, "m_bAnimGraphAdditive": 7, "m_bDelta": 3, "m_bHidden": 2, "m_bImplicitSeqIgnoreDelta": 6, "m_bLegacyWorldspace": 4, "m_bLooping": 0, "m_bModelDoc": 5}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimEncodeDifference": {"fields": {"m_bHasMorphBitArray": 120, "m_bHasMovementBitArray": 96, "m_bHasRotationBitArray": 72, "m_bHasUserBitArray": 144, "m_boneArray": 0, "m_morphArray": 24, "m_userArray": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimEncodedFrames": {"fields": {"m_fileName": 0, "m_frameblockArray": 24, "m_nFrames": 16, "m_nFramesPerBlock": 20, "m_usageDifferences": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimEnum": {"fields": {"m_value": 0}, "metadata": [{"name": "MPropertyEditClassAsString", "type": "Unknown"}], "parent": null}, "CAnimEventDefinition": {"fields": {"m_EventData": 24, "m_flCycle": 16, "m_flDuration": 20, "m_nEndFrame": 12, "m_nFrame": 8, "m_sEventName": 56, "m_sLegacyOptions": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimFoot": {"fields": {"m_ankleBoneIndex": 32, "m_name": 0, "m_toeBoneIndex": 36, "m_vBallOffset": 8, "m_vHeelOffset": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimFrameBlockAnim": {"fields": {"m_nEndFrame": 4, "m_nStartFrame": 0, "m_segmentIndexArray": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimFrameSegment": {"fields": {"m_container": 16, "m_nLocalChannel": 8, "m_nLocalElementMasks": 4, "m_nUniqueFrameIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimGraphDebugReplay": {"fields": {"m_animGraphFileName": 64, "m_frameCount": 104, "m_frameList": 72, "m_startIndex": 96, "m_writeIndex": 100}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimGraphModelBinding": {"fields": {"m_modelName": 8, "m_pSharedData": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimGraphNetworkSettings": {"fields": {"m_bNetworkingEnabled": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimGraphSettingsGroup"}, "CAnimGraphSettingsGroup": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimGraphSettingsManager": {"fields": {"m_settingsGroups": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimInputDamping": {"fields": {"m_fSpeedScale": 12, "m_speedFunction": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": null}, "CAnimKeyData": {"fields": {"m_boneArray": 16, "m_dataChannelArray": 96, "m_morphArray": 64, "m_nChannelElements": 88, "m_name": 0, "m_userArray": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimLocalHierarchy": {"fields": {"m_nEndFrame": 44, "m_nPeakFrame": 36, "m_nStartFrame": 32, "m_nTailFrame": 40, "m_sBone": 0, "m_sNewParent": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimMorphDifference": {"fields": {"m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimMotorUpdaterBase": {"fields": {"m_bDefault": 24, "m_name": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimMovement": {"fields": {"angle": 16, "endframe": 0, "motionflags": 4, "position": 32, "v0": 8, "v1": 12, "vector": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimNodePath": {"fields": {"m_nCount": 44, "m_path": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimParamHandle": {"fields": {"m_index": 1, "m_type": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimParamHandleMap": {"fields": {"m_list": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimParameterBase": {"fields": {"m_bIsReferenced": 105, "m_bNetworkingRequested": 104, "m_componentName": 72, "m_group": 40, "m_id": 48, "m_name": 24, "m_sComment": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimParameterManagerUpdater": {"fields": {"m_autoResetMap": 160, "m_autoResetParams": 136, "m_idToIndexMap": 48, "m_indexToHandle": 112, "m_nameToIndexMap": 80, "m_parameters": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimReplayFrame": {"fields": {"m_inputDataBlocks": 16, "m_instanceData": 40, "m_localToWorldTransform": 96, "m_startingLocalToWorldTransform": 64, "m_timeStamp": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimScriptComponentUpdater": {"fields": {"m_hScript": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CAnimScriptManager": {"fields": {"m_scriptInfo": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimSequenceParams": {"fields": {"m_flFadeInTime": 0, "m_flFadeOutTime": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimSkeleton": {"fields": {"m_boneNames": 64, "m_children": 88, "m_feet": 136, "m_localSpaceTransforms": 16, "m_lodBoneCounts": 184, "m_modelSpaceTransforms": 40, "m_morphNames": 160, "m_parents": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimStateMachineUpdater": {"fields": {"m_startStateIndex": 80, "m_states": 8, "m_transitions": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimTagBase": {"fields": {"m_bIsReferenced": 72, "m_group": 40, "m_name": 24, "m_sComment": 32, "m_tagID": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimTagManagerUpdater": {"fields": {"m_tags": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimUpdateNodeBase": {"fields": {"m_name": 80, "m_networkMode": 72, "m_nodePath": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimUpdateNodeRef": {"fields": {"m_nodeIndex": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimUpdateSharedData": {"fields": {"m_components": 72, "m_nodeIndexMap": 40, "m_nodes": 16, "m_pParamListUpdater": 96, "m_pSkeleton": 176, "m_pStaticPoseCache": 168, "m_pTagManagerUpdater": 104, "m_rootNodePath": 184, "m_scriptManager": 112, "m_settings": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimUser": {"fields": {"m_nType": 16, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimUserDifference": {"fields": {"m_nType": 16, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimationGraphVisualizerAxis": {"fields": {"m_flAxisSize": 96, "m_xWsTransform": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimationGraphVisualizerPrimitiveBase"}, "CAnimationGraphVisualizerLine": {"fields": {"m_Color": 96, "m_vWsPositionEnd": 80, "m_vWsPositionStart": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimationGraphVisualizerPrimitiveBase"}, "CAnimationGraphVisualizerPie": {"fields": {"m_Color": 112, "m_vWsCenter": 64, "m_vWsEnd": 96, "m_vWsStart": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimationGraphVisualizerPrimitiveBase"}, "CAnimationGraphVisualizerPrimitiveBase": {"fields": {"m_OwningAnimNodePaths": 12, "m_Type": 8, "m_nOwningAnimNodePathCount": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAnimationGraphVisualizerSphere": {"fields": {"m_Color": 84, "m_flRadius": 80, "m_vWsPosition": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimationGraphVisualizerPrimitiveBase"}, "CAnimationGraphVisualizerText": {"fields": {"m_Color": 80, "m_Text": 88, "m_vWsPosition": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimationGraphVisualizerPrimitiveBase"}, "CAnimationGroup": {"fields": {"m_AdditionalExtRefs": 296, "m_decodeKey": 152, "m_directHSeqGroup_Handle": 144, "m_includedGroupArray_Handle": 120, "m_localHAnimArray_Handle": 96, "m_nFlags": 16, "m_name": 24, "m_szScripts": 272}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAttachment": {"fields": {"m_bIgnoreRotation": 132, "m_bInfluenceRootTransform": 128, "m_influenceNames": 8, "m_influenceWeights": 116, "m_nInfluences": 131, "m_name": 0, "m_vInfluenceOffsets": 80, "m_vInfluenceRotations": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CAudioAnimTag": {"fields": {"m_attachmentName": 96, "m_bPlayOnClient": 111, "m_bPlayOnServer": 110, "m_bStopWhenGraphEnds": 109, "m_bStopWhenTagEnds": 108, "m_clipName": 88, "m_flVolume": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CBaseConstraint": {"fields": {"m_name": 40, "m_slaves": 64, "m_targets": 80, "m_vUpVector": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBoneConstraintBase"}, "CBinaryUpdateNode": {"fields": {"m_bResetChild1": 128, "m_bResetChild2": 129, "m_flTimingBlend": 124, "m_pChild1": 88, "m_pChild2": 104, "m_timingBehavior": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CBindPoseUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CBlend2DUpdateNode": {"fields": {"m_bAnimEventsAndTagsOnMostWeightedOnly": 235, "m_bLockBlendOnReset": 233, "m_bLockWhenWaning": 234, "m_bLoop": 232, "m_blendSourceX": 208, "m_blendSourceY": 216, "m_damping": 192, "m_eBlendMode": 224, "m_items": 96, "m_nodeItemIndices": 168, "m_paramSpans": 144, "m_paramX": 212, "m_paramY": 220, "m_playbackSpeed": 228, "m_tags": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CBlendCurve": {"fields": {"m_flControlPoint1": 0, "m_flControlPoint2": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CBlendUpdateNode": {"fields": {"m_bLockBlendOnReset": 204, "m_bLockWhenWaning": 207, "m_bLoop": 206, "m_bSyncCycles": 205, "m_blendKeyType": 200, "m_blendValueSource": 172, "m_children": 96, "m_damping": 184, "m_paramIndex": 176, "m_sortedOrder": 120, "m_targetValues": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CBlockSelectionMetricEvaluator": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CBodyGroupAnimTag": {"fields": {"m_bodyGroupSettings": 96, "m_nPriority": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CBodyGroupSetting": {"fields": {"m_BodyGroupName": 0, "m_nBodyGroupOption": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyElementNameFn", "type": "Unknown"}], "parent": null}, "CBoneConstraintBase": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CBoneConstraintDotToMorph": {"fields": {"m_flRemap": 64, "m_sBoneName": 40, "m_sMorphChannelName": 56, "m_sTargetBoneName": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBoneConstraintBase"}, "CBoneConstraintPoseSpaceBone": {"fields": {"m_inputList": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CBoneConstraintPoseSpaceBone__Input_t": {"fields": {"m_inputValue": 0, "m_outputTransformList": 16}, "metadata": [], "parent": null}, "CBoneConstraintPoseSpaceMorph": {"fields": {"m_bClamp": 104, "m_inputList": 80, "m_outputMorph": 56, "m_sAttachmentName": 48, "m_sBoneName": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBoneConstraintBase"}, "CBoneConstraintPoseSpaceMorph__Input_t": {"fields": {"m_inputValue": 0, "m_outputWeightList": 16}, "metadata": [], "parent": null}, "CBoneMaskUpdateNode": {"fields": {"m_bUseBlendScale": 156, "m_blendSpace": 148, "m_blendValueSource": 160, "m_flRootMotionBlend": 144, "m_footMotionTiming": 152, "m_hBlendParameter": 164, "m_nWeightListIndex": 140}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBinaryUpdateNode"}, "CBonePositionMetricEvaluator": {"fields": {"m_nBoneIndex": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CBoneVelocityMetricEvaluator": {"fields": {"m_nBoneIndex": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CBoolAnimParameter": {"fields": {"m_bDefaultValue": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CCPPScriptComponentUpdater": {"fields": {"m_scriptsToRun": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CCachedPose": {"fields": {"m_flCycle": 60, "m_hSequence": 56, "m_morphWeights": 32, "m_transforms": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CChoiceUpdateNode": {"fields": {"m_bCrossFade": 176, "m_bDontResetSameSelection": 178, "m_bResetChosen": 177, "m_blendMethod": 168, "m_blendTime": 172, "m_blendTimes": 136, "m_children": 88, "m_choiceChangeMethod": 164, "m_choiceMethod": 160, "m_weights": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CChoreoUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CClothSettingsAnimTag": {"fields": {"m_flEaseIn": 92, "m_flEaseOut": 96, "m_flStiffness": 88, "m_nVertexSet": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CCompressorGroup": {"fields": {"m_boolCompressor": 320, "m_colorCompressor": 344, "m_intCompressor": 296, "m_nCompressorIndex": 128, "m_nElementMask": 200, "m_nElementUniqueID": 176, "m_nFlags": 80, "m_nTotalElementCount": 0, "m_nType": 56, "m_quaternionCompressor": 272, "m_szChannelClass": 8, "m_szElementNames": 152, "m_szGrouping": 104, "m_szVariableName": 32, "m_vector2DCompressor": 368, "m_vector4DCompressor": 392, "m_vectorCompressor": 248}, "metadata": [], "parent": null}, "CConcreteAnimParameter": {"fields": {"m_bAutoReset": 121, "m_bGameWritable": 122, "m_bGraphWritable": 123, "m_bUseMostRecentValue": 120, "m_eNetworkSetting": 116, "m_previewButton": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimParameterBase"}, "CConstraintSlave": {"fields": {"m_flWeight": 32, "m_nBoneHash": 28, "m_qBaseOrientation": 0, "m_sName": 40, "m_vBasePosition": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CConstraintTarget": {"fields": {"m_bIsAttachment": 89, "m_flWeight": 72, "m_nBoneHash": 60, "m_qOffset": 32, "m_sName": 64, "m_vOffset": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CCurrentRotationVelocityMetricEvaluator": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CCurrentVelocityMetricEvaluator": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CCycleBase": {"fields": {"m_flCycle": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CCycleControlClipUpdateNode": {"fields": {"m_duration": 128, "m_hSequence": 124, "m_paramIndex": 136, "m_tags": 96, "m_valueSource": 132}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CCycleControlUpdateNode": {"fields": {"m_paramIndex": 108, "m_valueSource": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CDampedPathAnimMotorUpdater": {"fields": {"m_flAnticipationTime": 44, "m_flMaxSpringTension": 64, "m_flMinSpeedScale": 48, "m_flMinSpringTension": 60, "m_flSpringConstant": 56, "m_hAnticipationHeadingParam": 54, "m_hAnticipationPosParam": 52}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CPathAnimMotorUpdaterBase"}, "CDampedValueComponentUpdater": {"fields": {"m_items": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CDampedValueUpdateItem": {"fields": {"m_damping": 0, "m_hParamIn": 24, "m_hParamOut": 26}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CDemoSettingsComponentUpdater": {"fields": {"m_settings": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CDirectPlaybackTagData": {"fields": {"m_sequenceName": 0, "m_tags": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CDirectPlaybackUpdateNode": {"fields": {"m_allTags": 112, "m_bFinishEarly": 108, "m_bResetOnFinish": 109}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CDirectionalBlendUpdateNode": {"fields": {"m_bLockBlendOnReset": 161, "m_bLoop": 160, "m_blendValueSource": 144, "m_damping": 128, "m_duration": 156, "m_hSequences": 92, "m_paramIndex": 148, "m_playbackSpeed": 152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CDistanceRemainingMetricEvaluator": {"fields": {"m_bFilterFixedMinDistance": 96, "m_bFilterGoalDistance": 97, "m_bFilterGoalOvershoot": 98, "m_flMaxDistance": 80, "m_flMaxGoalOvershootScale": 92, "m_flMinDistance": 84, "m_flStartGoalFilterDistance": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CDrawCullingData": {"fields": {"m_ConeAxis": 12, "m_ConeCutoff": 15, "m_vConeApex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CEditableMotionGraph": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionGraph"}, "CEmitTagActionUpdater": {"fields": {"m_bIsZeroDuration": 28, "m_nTagIndex": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimActionUpdater"}, "CEnumAnimParameter": {"fields": {"m_defaultValue": 136, "m_enumOptions": 144, "m_vecEnumReferenced": 168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CExpressionActionUpdater": {"fields": {"m_eParamType": 26, "m_hParam": 24, "m_hScript": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimActionUpdater"}, "CFlexController": {"fields": {"m_szName": 0, "m_szType": 8, "max": 20, "min": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFlexDesc": {"fields": {"m_szFacs": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFlexOp": {"fields": {"m_Data": 4, "m_OpCode": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFlexRule": {"fields": {"m_FlexOps": 8, "m_nFlex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFloatAnimParameter": {"fields": {"m_bInterpolate": 140, "m_fDefaultValue": 128, "m_fMaxValue": 136, "m_fMinValue": 132}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CFollowAttachmentUpdateNode": {"fields": {"m_opFixedData": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFollowPathUpdateNode": {"fields": {"m_bBlockNonPathMovement": 112, "m_bScaleSpeed": 114, "m_bStopFeetAtGoal": 113, "m_bTurnToFace": 164, "m_facingTarget": 152, "m_flBlendOutTime": 108, "m_flMaxAngle": 124, "m_flMinAngle": 120, "m_flScale": 116, "m_flSpeedScaleBlending": 128, "m_flTurnToFaceOffset": 160, "m_hParam": 156, "m_turnDamping": 136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFollowTargetUpdateNode": {"fields": {"m_hParameterOrientation": 130, "m_hParameterPosition": 128, "m_opFixedData": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFootAdjustmentUpdateNode": {"fields": {"m_bAnimationDriven": 161, "m_bResetChild": 160, "m_clips": 112, "m_facingTarget": 140, "m_flStepHeightMax": 152, "m_flStepHeightMaxAngle": 156, "m_flTurnTimeMax": 148, "m_flTurnTimeMin": 144, "m_hBasePoseCacheHandle": 136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFootCycle": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CCycleBase"}, "CFootCycleDefinition": {"fields": {"m_flStanceDirectionMS": 24, "m_footLandCycle": 56, "m_footLiftCycle": 44, "m_footOffCycle": 48, "m_footStrikeCycle": 52, "m_stanceCycle": 40, "m_vMidpointPositionMS": 12, "m_vStancePositionMS": 0, "m_vToStrideStartPos": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootCycleMetricEvaluator": {"fields": {"m_footIndices": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CFootDefinition": {"fields": {"m_ankleBoneName": 8, "m_flBindPoseDirectionMS": 52, "m_flFootLength": 48, "m_flTraceHeight": 56, "m_flTraceRadius": 60, "m_name": 0, "m_toeBoneName": 16, "m_vBallOffset": 24, "m_vHeelOffset": 36}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootFallAnimTag": {"fields": {"m_foot": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CFootLockUpdateNode": {"fields": {"m_bApplyFootRotationLimits": 304, "m_bApplyHipShift": 305, "m_bEnableRootHeightDamping": 309, "m_bEnableVerticalCurvedPaths": 308, "m_bModulateStepHeight": 306, "m_bResetChild": 307, "m_flBlendTime": 284, "m_flHipShiftScale": 280, "m_flMaxRootHeightOffset": 288, "m_flMinRootHeightOffset": 292, "m_flStepHeightDecreaseScale": 276, "m_flStepHeightIncreaseScale": 272, "m_flStrideCurveLimitScale": 268, "m_flStrideCurveScale": 264, "m_flTiltPlanePitchSpringStrength": 296, "m_flTiltPlaneRollSpringStrength": 300, "m_footSettings": 208, "m_hipShiftDamping": 232, "m_opFixedSettings": 104, "m_rootHeightDamping": 248}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFootMotion": {"fields": {"m_bAdditive": 32, "m_name": 24, "m_strides": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootPinningUpdateNode": {"fields": {"m_bResetChild": 192, "m_eTimingSource": 160, "m_params": 168, "m_poseOpFixedData": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFootPositionMetricEvaluator": {"fields": {"m_bIgnoreSlope": 104, "m_footIndices": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CFootStepTriggerUpdateNode": {"fields": {"m_flTolerance": 132, "m_triggers": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CFootStride": {"fields": {"m_definition": 0, "m_trajectories": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootTrajectories": {"fields": {"m_trajectories": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootTrajectory": {"fields": {"m_flProgression": 16, "m_flRotationOffset": 12, "m_vOffset": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFootstepLandedAnimTag": {"fields": {"m_BoneName": 112, "m_DebugAnimSourceString": 104, "m_FootstepType": 88, "m_OverrideSoundName": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CFutureFacingMetricEvaluator": {"fields": {"m_flDistance": 80, "m_flTime": 84}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CFutureVelocityMetricEvaluator": {"fields": {"m_eMode": 92, "m_flDistance": 80, "m_flStoppingDistance": 84, "m_flTargetSpeed": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CHandshakeAnimTagBase": {"fields": {"m_bIsDisableTag": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CHitBox": {"fields": {"m_CRC": 64, "m_bTranslationOnly": 61, "m_cRenderColor": 68, "m_flShapeRadius": 48, "m_nBoneNameHash": 52, "m_nGroupId": 56, "m_nHitBoxIndex": 72, "m_nShapeType": 60, "m_name": 0, "m_sBoneName": 16, "m_sSurfaceProperty": 8, "m_vMaxBounds": 36, "m_vMinBounds": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CHitBoxSet": {"fields": {"m_HitBoxes": 16, "m_SourceFilename": 40, "m_nNameHash": 8, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CHitBoxSetList": {"fields": {"m_HitBoxSets": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CHitReactUpdateNode": {"fields": {"m_bResetChild": 196, "m_flMinDelayBetweenHits": 192, "m_hitBoneParam": 182, "m_hitDirectionParam": 186, "m_hitOffsetParam": 184, "m_hitStrengthParam": 188, "m_opFixedSettings": 104, "m_triggerParam": 180}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CInputStreamUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CIntAnimParameter": {"fields": {"m_defaultValue": 128, "m_maxValue": 136, "m_minValue": 132}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CJiggleBoneUpdateNode": {"fields": {"m_opFixedData": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CJumpHelperUpdateNode": {"fields": {"m_bScaleSpeed": 203, "m_bTranslationAxis": 200, "m_eCorrectionMethod": 196, "m_flJumpEndCycle": 192, "m_flJumpStartCycle": 188, "m_flOriginalJumpDuration": 184, "m_flOriginalJumpMovement": 172, "m_hTargetParam": 168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CSequenceUpdateNode"}, "CLODComponentUpdater": {"fields": {"m_nServerLOD": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CLeafUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CLeanMatrixUpdateNode": {"fields": {"m_blendSource": 184, "m_damping": 168, "m_flMaxValue": 220, "m_frameCorners": 92, "m_hSequence": 216, "m_horizontalAxis": 204, "m_nSequenceMaxFrame": 224, "m_paramIndex": 188, "m_poses": 128, "m_verticalAxis": 192}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CLookAtUpdateNode": {"fields": {"m_bLockWhenWaning": 321, "m_bResetChild": 320, "m_opFixedSettings": 112, "m_paramIndex": 316, "m_target": 312, "m_weightParamIndex": 318}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CLookComponentUpdater": {"fields": {"m_bNetworkLookTarget": 66, "m_hLookDirection": 60, "m_hLookDistance": 58, "m_hLookHeading": 52, "m_hLookHeadingVelocity": 54, "m_hLookPitch": 56, "m_hLookTarget": 62, "m_hLookTargetWorldSpace": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CMaterialAttributeAnimTag": {"fields": {"m_AttributeName": 88, "m_AttributeType": 96, "m_Color": 104, "m_flValue": 100}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CMaterialDrawDescriptor": {"fields": {"m_flAlpha": 16, "m_flUvDensity": 0, "m_indexBuffer": 152, "m_material": 200, "m_nBaseVertex": 36, "m_nFirstMeshlet": 28, "m_nIndexCount": 48, "m_nNumMeshlets": 22, "m_nPrimitiveType": 32, "m_nStartIndex": 44, "m_nVertexCount": 40, "m_vTintColor": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMeshletDescriptor": {"fields": {"m_CullingData": 8, "m_PackedAABB": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CModelConfig": {"fields": {"m_ConfigName": 0, "m_Elements": 8, "m_bTopLevel": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CModelConfigElement": {"fields": {"m_ElementName": 8, "m_NestedElements": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CModelConfigElement_AttachedModel": {"fields": {"m_AttachmentName": 120, "m_AttachmentType": 136, "m_BodygroupOnOtherModels": 144, "m_EntityClass": 80, "m_InstanceName": 72, "m_LocalAttachmentOffsetName": 128, "m_MaterialGroupOnOtherModels": 152, "m_aAngOffset": 108, "m_bAcceptParentMaterialDrivenDecals": 143, "m_bBoneMergeFlex": 140, "m_bUserSpecifiedColor": 141, "m_bUserSpecifiedMaterialGroup": 142, "m_hModel": 88, "m_vOffset": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_Command": {"fields": {"m_Args": 80, "m_Command": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_RandomColor": {"fields": {"m_Gradient": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_RandomPick": {"fields": {"m_ChoiceWeights": 96, "m_Choices": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_SetBodygroup": {"fields": {"m_GroupName": 72, "m_nChoice": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_SetBodygroupOnAttachedModels": {"fields": {"m_GroupName": 72, "m_nChoice": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_SetMaterialGroup": {"fields": {"m_MaterialGroupName": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_SetMaterialGroupOnAttachedModels": {"fields": {"m_MaterialGroupName": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_SetRenderColor": {"fields": {"m_Color": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigElement_UserPick": {"fields": {"m_Choices": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CModelConfigElement"}, "CModelConfigList": {"fields": {"m_Configs": 8, "m_bHideMaterialGroupInTools": 0, "m_bHideRenderColorInTools": 1}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMoodVData": {"fields": {"m_animationLayers": 232, "m_nMoodType": 224, "m_sModelName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MVDataOverlayType", "type": "Unknown"}], "parent": null}, "CMorphBundleData": {"fields": {"m_flULeftSrc": 0, "m_flVTopSrc": 4, "m_offsets": 8, "m_ranges": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMorphConstraint": {"fields": {"m_flMax": 120, "m_flMin": 116, "m_nSlaveChannel": 112, "m_sTargetMorph": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CMorphData": {"fields": {"m_morphRectDatas": 8, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMorphRectData": {"fields": {"m_bundleDatas": 16, "m_flUWidthSrc": 4, "m_flVHeightSrc": 8, "m_nXLeftDst": 0, "m_nYTopDst": 2}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMorphSetData": {"fields": {"m_FlexControllers": 104, "m_FlexDesc": 80, "m_FlexRules": 128, "m_bundleTypes": 24, "m_morphDatas": 48, "m_nHeight": 20, "m_nWidth": 16, "m_pTextureAtlas": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionDataSet": {"fields": {"m_groups": 0, "m_nDimensionCount": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionGraph": {"fields": {"m_bLoop": 84, "m_nConfigCount": 80, "m_nConfigStartIndex": 76, "m_nParameterCount": 72, "m_pRootNode": 64, "m_paramSpans": 16, "m_tags": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionGraphConfig": {"fields": {"m_flDuration": 16, "m_nMotionIndex": 20, "m_nSampleCount": 28, "m_nSampleStart": 24, "m_paramValues": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionGraphGroup": {"fields": {"m_hIsActiveScript": 256, "m_motionGraphConfigs": 208, "m_motionGraphs": 184, "m_sampleToConfig": 232, "m_searchDB": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionGraphUpdateNode": {"fields": {"m_pMotionGraph": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CMotionMatchingUpdateNode": {"fields": {"m_bEnableDistanceScaling": 312, "m_bEnableRotationCorrection": 264, "m_bGoalAssist": 265, "m_bLockClipWhenWaning": 252, "m_bSearchEveryTick": 224, "m_bSearchWhenClipEnds": 232, "m_bSearchWhenGoalChanges": 233, "m_blendCurve": 236, "m_dataSet": 88, "m_distanceScale_Damping": 280, "m_flBlendTime": 248, "m_flDistanceScale_InnerRadius": 300, "m_flDistanceScale_MaxScale": 304, "m_flDistanceScale_MinScale": 308, "m_flDistanceScale_OuterRadius": 296, "m_flGoalAssistDistance": 268, "m_flGoalAssistTolerance": 272, "m_flReselectionTimeWindow": 260, "m_flSampleRate": 244, "m_flSearchInterval": 228, "m_flSelectionThreshold": 256, "m_metrics": 120, "m_weights": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CMotionMetricEvaluator": {"fields": {"m_flWeight": 72, "m_means": 24, "m_nDimensionStartIndex": 76, "m_standardDeviations": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionNode": {"fields": {"m_id": 32, "m_name": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionNodeBlend1D": {"fields": {"m_blendItems": 40, "m_nParamIndex": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionNode"}, "CMotionNodeSequence": {"fields": {"m_flPlaybackSpeed": 68, "m_hSequence": 64, "m_tags": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionNode"}, "CMotionSearchDB": {"fields": {"m_codeIndices": 160, "m_residualQuantizer": 128, "m_rootNode": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMotionSearchNode": {"fields": {"m_children": 0, "m_quantizer": 24, "m_sampleCodes": 56, "m_sampleIndices": 80, "m_selectableSamples": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CMovementComponentUpdater": {"fields": {"m_bMoveVarsDisabled": 104, "m_bNetworkFacing": 106, "m_bNetworkPath": 105, "m_facingDamping": 72, "m_flDefaultRunSpeed": 100, "m_motors": 48, "m_nDefaultMotorIndex": 96, "m_paramHandles": 107}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CMovementHandshakeAnimTag": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CHandshakeAnimTagBase"}, "CMoverUpdateNode": {"fields": {"m_bAdditive": 148, "m_bApplyMovement": 149, "m_bApplyRotation": 151, "m_bLimitOnly": 152, "m_bOrientMovement": 150, "m_damping": 112, "m_facingTarget": 128, "m_flTurnToFaceLimit": 144, "m_flTurnToFaceOffset": 140, "m_hMoveHeadingParam": 134, "m_hMoveVecParam": 132, "m_hTurnToFaceParam": 136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CNmAdditiveBlendTask": {"fields": {}, "metadata": [], "parent": "CNmBlendTaskBase"}, "CNmAndNode__CDefinition": {"fields": {"m_conditionNodeIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmAnimationPoseNode__CDefinition": {"fields": {"m_bUseFramesAsInput": 32, "m_flUserSpecifiedTime": 28, "m_inputTimeRemapRange": 20, "m_nDataSlotIdx": 18, "m_nPoseTimeValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmBitFlags": {"fields": {"m_flags": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmBlend1DNode__CDefinition": {"fields": {"m_parameterization": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmParameterizedBlendNode::CDefinition"}, "CNmBlend2DNode__CDefinition": {"fields": {"m_bAllowLooping": 264, "m_hullIndices": 224, "m_indices": 168, "m_nInputParameterNodeIdx0": 56, "m_nInputParameterNodeIdx1": 58, "m_sourceNodeIndices": 16, "m_values": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmBlendTask": {"fields": {}, "metadata": [], "parent": "CNmBlendTaskBase"}, "CNmBlendTaskBase": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "CNmBoneMask": {"fields": {"m_ID": 0, "m_weightInfo": 8, "m_weights": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmBoneMaskBlendNode__CDefinition": {"fields": {"m_nBlendWeightValueNodeIdx": 20, "m_nSourceMaskNodeIdx": 16, "m_nTargetMaskNodeIdx": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoneMaskValueNode::CDefinition"}, "CNmBoneMaskNode__CDefinition": {"fields": {"m_boneMaskID": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoneMaskValueNode::CDefinition"}, "CNmBoneMaskSelectorNode__CDefinition": {"fields": {"m_defaultMaskNodeIdx": 16, "m_flBlendTimeSeconds": 144, "m_maskNodeIndices": 24, "m_parameterValueNodeIdx": 18, "m_parameterValues": 64, "m_switchDynamically": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoneMaskValueNode::CDefinition"}, "CNmBoneMaskValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmBoolValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmCachedBoolNode__CDefinition": {"fields": {"m_mode": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmCachedFloatNode__CDefinition": {"fields": {"m_mode": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmCachedIDNode__CDefinition": {"fields": {"m_mode": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmCachedPoseReadTask": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "CNmCachedPoseWriteTask": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "CNmCachedTargetNode__CDefinition": {"fields": {"m_mode": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmTargetValueNode::CDefinition"}, "CNmCachedVectorNode__CDefinition": {"fields": {"m_mode": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmChildGraphNode__CDefinition": {"fields": {"m_nChildGraphIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmClip": {"fields": {"m_bIsAdditive": 416, "m_compressedPoseData": 16, "m_compressedPoseOffsets": 64, "m_flDuration": 12, "m_nNumFrames": 8, "m_rootMotion": 336, "m_skeleton": 0, "m_syncTrack": 160, "m_trackCompressionSettings": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmClipNode__CDefinition": {"fields": {"m_bAllowLooping": 21, "m_bSampleRootMotion": 20, "m_nDataSlotIdx": 22, "m_nPlayInReverseValueNodeIdx": 16, "m_nResetTimeValueNodeIdx": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmClipSelectorNode__CDefinition": {"fields": {"m_conditionNodeIndices": 40, "m_optionNodeIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmConstBoolNode__CDefinition": {"fields": {"m_bValue": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmConstFloatNode__CDefinition": {"fields": {"m_flValue": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmConstIDNode__CDefinition": {"fields": {"m_value": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmConstTargetNode__CDefinition": {"fields": {"m_value": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmTargetValueNode::CDefinition"}, "CNmConstVectorNode__CDefinition": {"fields": {"m_value": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmControlParameterBoolNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmControlParameterFloatNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmControlParameterIDNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmControlParameterTargetNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmTargetValueNode::CDefinition"}, "CNmControlParameterVectorNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmCurrentSyncEventIDNode__CDefinition": {"fields": {"m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmCurrentSyncEventIndexNode__CDefinition": {"fields": {"m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmCurrentSyncEventPercentageThroughNode__CDefinition": {"fields": {"m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmDurationScaleNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmSpeedScaleBaseNode::CDefinition"}, "CNmEvent": {"fields": {"m_flDurationSeconds": 12, "m_flStartTimeSeconds": 8, "m_syncID": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmExternalGraphNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmFixedWeightBoneMaskNode__CDefinition": {"fields": {"m_flBoneWeight": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoneMaskValueNode::CDefinition"}, "CNmFloatAbsNode__CDefinition": {"fields": {"m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatAngleMathNode__CDefinition": {"fields": {"m_nInputValueNodeIdx": 16, "m_operation": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatClampNode__CDefinition": {"fields": {"m_clampRange": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatComparisonNode__CDefinition": {"fields": {"m_comparison": 20, "m_flComparisonValue": 28, "m_flEpsilon": 24, "m_nComparandValueNodeIdx": 18, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmFloatCurveNode__CDefinition": {"fields": {"m_curve": 24, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatEaseNode__CDefinition": {"fields": {"m_bUseStartValue": 27, "m_easingOp": 26, "m_flEaseTime": 16, "m_flStartValue": 20, "m_nInputValueNodeIdx": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatMathNode__CDefinition": {"fields": {"m_bReturnAbsoluteResult": 20, "m_flValueB": 24, "m_nInputValueNodeIdxA": 16, "m_nInputValueNodeIdxB": 18, "m_operator": 21}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatRangeComparisonNode__CDefinition": {"fields": {"m_bIsInclusiveCheck": 26, "m_nInputValueNodeIdx": 24, "m_range": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmFloatRemapNode__CDefinition": {"fields": {"m_inputRange": 20, "m_nInputValueNodeIdx": 16, "m_outputRange": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatRemapNode__RemapRange_t": {"fields": {"m_flBegin": 0, "m_flEnd": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmFloatSelectorNode__CDefinition": {"fields": {"m_conditionNodeIndices": 16, "m_easingOp": 112, "m_flDefaultValue": 104, "m_flEaseTime": 108, "m_values": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatSwitchNode__CDefinition": {"fields": {"m_nFalseValueNodeIdx": 20, "m_nSwitchValueNodeIdx": 16, "m_nTrueValueNodeIdx": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFloatValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmFootEvent": {"fields": {"m_phase": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmFootEventConditionNode__CDefinition": {"fields": {"m_eventConditionRules": 20, "m_nSourceStateNodeIdx": 16, "m_phaseCondition": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmFootstepEventIDNode__CDefinition": {"fields": {"m_eventConditionRules": 20, "m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmFootstepEventPercentageThroughNode__CDefinition": {"fields": {"m_eventConditionRules": 20, "m_nSourceStateNodeIdx": 16, "m_phaseCondition": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmFrameSnapEvent": {"fields": {"m_frameSnapMode": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmGraphDataSet": {"fields": {"m_resources": 16, "m_skeleton": 8, "m_variationID": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmGraphDefinition": {"fields": {"m_childGraphSlots": 104, "m_controlParameterIDs": 32, "m_externalGraphSlots": 128, "m_nRootNodeIdx": 24, "m_nodePaths": 264, "m_persistentNodeIndices": 0, "m_runtimeVersionID": 288, "m_virtualParameterIDs": 56, "m_virtualParameterNodeIndices": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmGraphDefinition__ChildGraphSlot_t": {"fields": {"m_dataSlotIdx": 2, "m_nNodeIdx": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmGraphDefinition__ExternalGraphSlot_t": {"fields": {"m_nNodeIdx": 0, "m_slotID": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmGraphNode__CDefinition": {"fields": {"m_nNodeIdx": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmGraphVariation": {"fields": {"m_dataSet": 8, "m_graphDefinition": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmIDComparisonNode__CDefinition": {"fields": {"m_comparisionIDs": 24, "m_comparison": 18, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmIDEvent": {"fields": {"m_ID": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmIDEventConditionNode__CDefinition": {"fields": {"m_eventConditionRules": 20, "m_eventIDs": 24, "m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmIDEventNode__CDefinition": {"fields": {"m_defaultValue": 24, "m_eventConditionRules": 20, "m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmIDEventPercentageThroughNode__CDefinition": {"fields": {"m_eventConditionRules": 20, "m_eventID": 24, "m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmIDToFloatNode__CDefinition": {"fields": {"m_IDs": 24, "m_defaultValue": 20, "m_nInputValueNodeIdx": 16, "m_values": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmIDValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmIKRig": {"fields": {"m_skeleton": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmIsTargetSetNode__CDefinition": {"fields": {"m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmLayerBlendNode__CDefinition": {"fields": {"m_bOnlySampleBaseRootMotion": 18, "m_layerDefinition": 24, "m_nBaseNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmLayerBlendNode__LayerDefinition_t": {"fields": {"m_bIgnoreEvents": 9, "m_bIsStateMachineLayer": 10, "m_bIsSynchronized": 8, "m_blendMode": 11, "m_nBoneMaskValueNodeIdx": 4, "m_nInputNodeIdx": 0, "m_nRootMotionWeightValueNodeIdx": 6, "m_nWeightValueNodeIdx": 2}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmLegacyEvent": {"fields": {"m_KV": 32, "m_animEventClassName": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmModelSpaceBlendTask": {"fields": {}, "metadata": [], "parent": "CNmBlendTaskBase"}, "CNmNotNode__CDefinition": {"fields": {"m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmOrNode__CDefinition": {"fields": {"m_conditionNodeIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmOrientationWarpEvent": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmOrientationWarpNode__CDefinition": {"fields": {"m_bIsOffsetNode": 20, "m_bIsOffsetRelativeToCharacter": 21, "m_nClipReferenceNodeIdx": 16, "m_nTargetValueNodeIdx": 18, "m_samplingMode": 22}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmOverlayBlendTask": {"fields": {}, "metadata": [], "parent": "CNmBlendTaskBase"}, "CNmParameterizedBlendNode__BlendRange_t": {"fields": {"m_nInputIdx0": 0, "m_nInputIdx1": 2, "m_parameterValueRange": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmParameterizedBlendNode__CDefinition": {"fields": {"m_bAllowLooping": 58, "m_nInputParameterValueNodeIdx": 56, "m_sourceNodeIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmParameterizedBlendNode__Parameterization_t": {"fields": {"m_blendRanges": 0, "m_parameterRange": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmParameterizedClipSelectorNode__CDefinition": {"fields": {"m_optionNodeIndices": 16, "m_parameterNodeIdx": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmParameterizedSelectorNode__CDefinition": {"fields": {"m_optionNodeIndices": 16, "m_parameterNodeIdx": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmPassthroughNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmPoseNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmGraphNode::CDefinition"}, "CNmReferencePoseNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmReferencePoseTask": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "CNmRootMotionData": {"fields": {"m_flAverageAngularVelocityRadians": 32, "m_flAverageLinearVelocity": 28, "m_nNumFrames": 24, "m_totalDelta": 48, "m_transforms": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmRootMotionEvent": {"fields": {"m_flBlendTimeSeconds": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmRootMotionOverrideNode__CDefinition": {"fields": {"m_angularVelocityLimitNodeIdx": 30, "m_desiredFacingDirectionNodeIdx": 26, "m_desiredMovingVelocityNodeIdx": 24, "m_linearVelocityLimitNodeIdx": 28, "m_maxAngularVelocityRadians": 36, "m_maxLinearVelocity": 32, "m_overrideFlags": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPassthroughNode::CDefinition"}, "CNmSampleTask": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "CNmSelectorNode__CDefinition": {"fields": {"m_conditionNodeIndices": 40, "m_optionNodeIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmSkeleton": {"fields": {"m_ID": 0, "m_boneIDs": 8, "m_boneMasks": 104, "m_modelSpaceReferencePose": 72, "m_numBonesToSampleAtLowLOD": 96, "m_parentIndices": 24, "m_parentSpaceReferencePose": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmSpeedScaleBaseNode__CDefinition": {"fields": {"m_flDefaultInputValue": 28, "m_nInputValueNodeIdx": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPassthroughNode::CDefinition"}, "CNmSpeedScaleNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmSpeedScaleBaseNode::CDefinition"}, "CNmStateCompletedConditionNode__CDefinition": {"fields": {"m_flTransitionDurationSeconds": 20, "m_nSourceStateNodeIdx": 16, "m_nTransitionDurationOverrideNodeIdx": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmStateEventConditionNode__CDefinition": {"fields": {"m_conditions": 24, "m_eventConditionRules": 20, "m_nSourceStateNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmStateEventConditionNode__Condition_t": {"fields": {"m_eventID": 0, "m_eventTypeCondition": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmStateMachineNode__CDefinition": {"fields": {"m_nDefaultStateIndex": 304, "m_stateDefinitions": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmStateMachineNode__StateDefinition_t": {"fields": {"m_nEntryConditionNodeIdx": 2, "m_nStateNodeIdx": 0, "m_transitionDefinitions": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmStateMachineNode__TransitionDefinition_t": {"fields": {"m_bCanBeForced": 6, "m_nConditionNodeIdx": 2, "m_nTargetStateIdx": 0, "m_nTransitionNodeIdx": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmStateNode__CDefinition": {"fields": {"m_bIsOffState": 174, "m_entryEvents": 24, "m_executeEvents": 56, "m_exitEvents": 88, "m_nChildNodeIdx": 16, "m_nLayerBoneMaskNodeIdx": 172, "m_nLayerRootMotionWeightNodeIdx": 170, "m_nLayerWeightNodeIdx": 168, "m_timedElapsedEvents": 144, "m_timedRemainingEvents": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmStateNode__TimedEvent_t": {"fields": {"m_ID": 0, "m_flTimeValueSeconds": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmSyncEventIndexConditionNode__CDefinition": {"fields": {"m_nSourceStateNodeIdx": 16, "m_syncEventIdx": 20, "m_triggerMode": 18}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmSyncTrack": {"fields": {"m_nStartEventOffset": 168, "m_syncEvents": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmSyncTrack__EventMarker_t": {"fields": {"m_ID": 8, "m_startTime": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmSyncTrack__Event_t": {"fields": {"m_ID": 0, "m_duration": 12, "m_startTime": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmTarget": {"fields": {"m_bHasOffsets": 42, "m_bIsBoneTarget": 40, "m_bIsSet": 43, "m_bIsUsingBoneSpaceOffsets": 41, "m_boneID": 32, "m_transform": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CNmTargetInfoNode__CDefinition": {"fields": {"m_bIsWorldSpaceTarget": 24, "m_infoType": 20, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmTargetOffsetNode__CDefinition": {"fields": {"m_bIsBoneSpaceOffset": 18, "m_nInputValueNodeIdx": 16, "m_rotationOffset": 32, "m_translationOffset": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmTargetValueNode::CDefinition"}, "CNmTargetPointNode__CDefinition": {"fields": {"m_bIsWorldSpaceTarget": 18, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmTargetValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmTargetWarpEvent": {"fields": {"m_algorithm": 25, "m_rule": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmTargetWarpNode__CDefinition": {"fields": {"m_bAllowTargetUpdate": 21, "m_flLerpFallbackDistanceThreshold": 32, "m_flMaxTangentLength": 28, "m_flSamplingPositionErrorThresholdSq": 24, "m_flTargetUpdateAngleThresholdRadians": 40, "m_flTargetUpdateDistanceThreshold": 36, "m_nClipReferenceNodeIdx": 16, "m_nTargetValueNodeIdx": 18, "m_samplingMode": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmTask": {"fields": {}, "metadata": [], "parent": null}, "CNmTimeConditionNode__CDefinition": {"fields": {"m_flComparand": 20, "m_nInputValueNodeIdx": 18, "m_operator": 25, "m_sourceStateNodeIdx": 16, "m_type": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmTransitionEvent": {"fields": {"m_ID": 32, "m_rule": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmEvent"}, "CNmTransitionEventConditionNode__CDefinition": {"fields": {"m_eventConditionRules": 24, "m_nSourceStateNodeIdx": 28, "m_requireRuleID": 16, "m_ruleCondition": 30}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmTransitionNode__CDefinition": {"fields": {"m_blendWeightEasing": 42, "m_boneMaskBlendInTimePercentage": 28, "m_flDuration": 24, "m_nDurationOverrideNodeIdx": 18, "m_nTargetStateNodeIdx": 16, "m_rootMotionBlend": 43, "m_startBoneMaskNodeIdx": 22, "m_syncEventOffset": 32, "m_syncEventOffsetOverrideNodeIdx": 20, "m_targetSyncIDNodeIdx": 40, "m_transitionOptions": 36}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmGraphNode::CDefinition"}, "CNmVectorCreateNode__CDefinition": {"fields": {"m_inputValueXNodeIdx": 18, "m_inputValueYNodeIdx": 20, "m_inputValueZNodeIdx": 22, "m_inputVectorValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmVectorInfoNode__CDefinition": {"fields": {"m_desiredInfo": 18, "m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmVectorNegateNode__CDefinition": {"fields": {"m_nInputValueNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmVectorValueNode__CDefinition": {"fields": {}, "metadata": [], "parent": "CNmValueNode::CDefinition"}, "CNmVelocityBasedSpeedScaleNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmSpeedScaleBaseNode::CDefinition"}, "CNmVelocityBlendNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmParameterizedBlendNode::CDefinition"}, "CNmVirtualParameterBoneMaskNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoneMaskValueNode::CDefinition"}, "CNmVirtualParameterBoolNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmBoolValueNode::CDefinition"}, "CNmVirtualParameterFloatNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmFloatValueNode::CDefinition"}, "CNmVirtualParameterIDNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmIDValueNode::CDefinition"}, "CNmVirtualParameterTargetNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmTargetValueNode::CDefinition"}, "CNmVirtualParameterVectorNode__CDefinition": {"fields": {"m_nChildNodeIdx": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmVectorValueNode::CDefinition"}, "CNmZeroPoseNode__CDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CNmPoseNode::CDefinition"}, "CNmZeroPoseTask": {"fields": {}, "metadata": [], "parent": "CNmTask"}, "COrientConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "COrientationWarpUpdateNode": {"fields": {"m_hFacingPositionParameter": 108, "m_turnDamping": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CPairedSequenceComponentUpdater": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CPairedSequenceUpdateNode": {"fields": {"m_sPairedSequenceRole": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CSequenceUpdateNodeBase"}, "CParamSpanUpdater": {"fields": {"m_spans": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParentConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CParticleAnimTag": {"fields": {"m_attachmentCP1Name": 136, "m_attachmentCP1Type": 144, "m_attachmentName": 120, "m_attachmentType": 128, "m_bDetachFromOwner": 112, "m_bStopWhenTagEnds": 113, "m_bTagEndStopIsInstant": 114, "m_configName": 104, "m_hParticleSystem": 88, "m_particleSystemName": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CPathAnimMotorUpdater": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CPathAnimMotorUpdaterBase"}, "CPathAnimMotorUpdaterBase": {"fields": {"m_bLockToPath": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimMotorUpdaterBase"}, "CPathHelperUpdateNode": {"fields": {"m_flStoppingRadius": 104, "m_flStoppingSpeedScale": 108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CPathMetricEvaluator": {"fields": {"m_bExtrapolateMovement": 108, "m_flDistance": 104, "m_flMinExtrapolationSpeed": 112, "m_pathTimeSamples": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CPhysSurfaceProperties": {"fields": {"m_audioParams": 168, "m_audioSounds": 80, "m_bHidden": 24, "m_baseNameHash": 12, "m_description": 32, "m_name": 0, "m_nameHash": 8, "m_physics": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPhysSurfacePropertiesAudio": {"fields": {"m_flOcclusionFactor": 28, "m_flStaticImpactVolume": 24, "m_hardThreshold": 16, "m_hardVelocityThreshold": 20, "m_hardnessFactor": 4, "m_reflectivity": 0, "m_roughThreshold": 12, "m_roughnessFactor": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPhysSurfacePropertiesPhysics": {"fields": {"m_density": 8, "m_elasticity": 4, "m_flashpoint": 32, "m_friction": 0, "m_heatConductivity": 28, "m_softContactDampingRatio": 20, "m_softContactFrequency": 16, "m_thickness": 12, "m_wheelDrag": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPhysSurfacePropertiesSoundNames": {"fields": {"m_break": 48, "m_bulletImpact": 32, "m_impactHard": 8, "m_impactSoft": 0, "m_meleeImpact": 64, "m_pushOff": 72, "m_rolling": 40, "m_scrapeRough": 24, "m_scrapeSmooth": 16, "m_skidStop": 80, "m_strain": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPlayerInputAnimMotorUpdater": {"fields": {"m_bUseAcceleration": 72, "m_flAnticipationDistance": 64, "m_flSpringConstant": 60, "m_hAnticipationHeadingParam": 70, "m_hAnticipationPosParam": 68, "m_sampleTimes": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimMotorUpdaterBase"}, "CPointConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CPoseHandle": {"fields": {"m_eType": 2, "m_nIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CProductQuantizer": {"fields": {"m_nDimensions": 24, "m_subQuantizers": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CQuaternionAnimParameter": {"fields": {"m_bInterpolate": 144, "m_defaultValue": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CRagdollAnimTag": {"fields": {"m_bDestroy": 108, "m_flDampingRatio": 96, "m_flDecayBias": 104, "m_flDecayDuration": 100, "m_flFrequency": 92, "m_nPoseControl": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CRagdollComponentUpdater": {"fields": {"m_bSolidCollisionAtZeroWeight": 156, "m_boneIndices": 72, "m_boneNames": 96, "m_flMaxStretch": 152, "m_flSpringFrequencyMax": 148, "m_flSpringFrequencyMin": 144, "m_ragdollNodePaths": 48, "m_weightLists": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CRagdollUpdateNode": {"fields": {"m_nWeightListIndex": 104, "m_poseControlMethod": 108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CRenderBufferBinding": {"fields": {"m_hBuffer": 0, "m_nBindOffsetBytes": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CRenderGroom": {"fields": {"m_bEnableSimulation": 100, "m_hSimParamsMat": 64, "m_hairPositionOffsets": 24, "m_hairs": 0, "m_nAttachBoneIdx": 88, "m_nAttachMeshDrawCallIdx": 96, "m_nAttachMeshIdx": 92, "m_nGroomGroupID": 84, "m_nGuideHairCount": 76, "m_nHairCount": 80, "m_nSegmentsPerHairStrand": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CRenderMesh": {"fields": {"m_constraints": 120, "m_meshDeformParams": 424, "m_pGroomData": 440, "m_sceneObjects": 16, "m_skeleton": 136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CRenderSkeleton": {"fields": {"m_boneParents": 48, "m_bones": 0, "m_nBoneWeightCount": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CRootUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CSceneObjectData": {"fields": {"m_drawBounds": 40, "m_drawCalls": 24, "m_meshlets": 56, "m_vMaxBounds": 12, "m_vMinBounds": 0, "m_vTintColor": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSelectorUpdateNode": {"fields": {"m_bLockWhenWaning": 169, "m_bResetOnChange": 168, "m_bSyncCyclesOnChange": 170, "m_blendCurve": 140, "m_children": 88, "m_eTagBehavior": 164, "m_flBlendTime": 148, "m_hParameter": 156, "m_nTagIndex": 160, "m_tags": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CSeqAutoLayer": {"fields": {"m_end": 24, "m_flags": 4, "m_nLocalPose": 2, "m_nLocalReference": 0, "m_peak": 16, "m_start": 12, "m_tail": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqAutoLayerFlag": {"fields": {"m_bFetchFrame": 6, "m_bLocal": 4, "m_bNoBlend": 3, "m_bPose": 5, "m_bPost": 0, "m_bSpline": 1, "m_bSubtract": 7, "m_bXFade": 2}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqBoneMaskList": {"fields": {"m_flBoneWeightArray": 40, "m_flDefaultMorphCtrlWeight": 64, "m_morphCtrlWeightArray": 72, "m_nLocalBoneArray": 16, "m_sName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqCmdLayer": {"fields": {"m_bSpline": 10, "m_cmd": 0, "m_flVar1": 12, "m_flVar2": 16, "m_nDstResult": 6, "m_nLineNumber": 20, "m_nLocalBonemask": 4, "m_nLocalReference": 2, "m_nSrcResult": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqCmdSeqDesc": {"fields": {"m_activityArray": 96, "m_cmdLayerArray": 48, "m_eventArray": 72, "m_flFPS": 40, "m_flags": 16, "m_nFrameCount": 38, "m_nFrameRangeSequence": 36, "m_nSubCycles": 44, "m_numLocalResults": 46, "m_poseSettingArray": 120, "m_sName": 0, "m_transition": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqIKLock": {"fields": {"m_bBonesOrientedAlongPositiveX": 10, "m_flAngleWeight": 4, "m_flPosWeight": 0, "m_nLocalBone": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqMultiFetch": {"fields": {"m_bCalculatePoseParameters": 100, "m_bFixedBlendWeight": 101, "m_flFixedBlendWeightVals": 104, "m_flags": 0, "m_localReferenceArray": 8, "m_nGroupSize": 32, "m_nLocalCyclePoseParameter": 96, "m_nLocalPose": 40, "m_poseKeyArray0": 48, "m_poseKeyArray1": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqMultiFetchFlag": {"fields": {"m_b0D": 2, "m_b1D": 3, "m_b2D": 4, "m_b2D_TRI": 5, "m_bCylepose": 1, "m_bRealtime": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqPoseParamDesc": {"fields": {"m_bLooping": 28, "m_flEnd": 20, "m_flLoop": 24, "m_flStart": 16, "m_sName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqPoseSetting": {"fields": {"m_bX": 52, "m_bY": 53, "m_bZ": 54, "m_eType": 56, "m_flValue": 48, "m_sAttachment": 16, "m_sPoseParameter": 0, "m_sReferenceSequence": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqS1SeqDesc": {"fields": {"m_IKLockArray": 176, "m_LegacyKeyValueText": 224, "m_SequenceKeys": 208, "m_activityArray": 240, "m_autoLayerArray": 152, "m_fetch": 32, "m_flags": 16, "m_footMotion": 264, "m_nLocalWeightlist": 144, "m_sName": 0, "m_transition": 200}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqScaleSet": {"fields": {"m_bRootOffset": 16, "m_flBoneScaleArray": 56, "m_nLocalBoneArray": 32, "m_sName": 0, "m_vRootOffset": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqSeqDescFlag": {"fields": {"m_bAutoplay": 2, "m_bHidden": 4, "m_bLegacyCyclepose": 8, "m_bLegacyDelta": 6, "m_bLegacyRealtime": 9, "m_bLegacyWorldspace": 7, "m_bLooping": 0, "m_bModelDoc": 10, "m_bMulti": 5, "m_bPost": 3, "m_bSnap": 1}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqSynthAnimDesc": {"fields": {"m_activityArray": 40, "m_flags": 16, "m_nLocalBaseReference": 36, "m_nLocalBoneMask": 38, "m_sName": 0, "m_transition": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSeqTransition": {"fields": {"m_flFadeInTime": 0, "m_flFadeOutTime": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSequenceFinishedAnimTag": {"fields": {"m_sequenceName": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CSequenceGroupData": {"fields": {"m_keyValues": 272, "m_localBoneMaskArray": 160, "m_localBoneNameArray": 208, "m_localCmdSeqDescArray": 136, "m_localIKAutoplayLockArray": 288, "m_localMultiSeqDescArray": 88, "m_localNodeName": 232, "m_localPoseParamArray": 248, "m_localS1SeqDescArray": 64, "m_localScaleSetArray": 184, "m_localSequenceNameArray": 40, "m_localSynthAnimDescArray": 112, "m_nFlags": 32, "m_sName": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSequenceTagSpans": {"fields": {"m_sSequenceName": 0, "m_tags": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSequenceUpdateNode": {"fields": {"m_duration": 116, "m_hSequence": 112, "m_paramSpans": 120, "m_tags": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CSequenceUpdateNodeBase"}, "CSequenceUpdateNodeBase": {"fields": {"m_bLoop": 104, "m_playbackSpeed": 100}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CSetParameterActionUpdater": {"fields": {"m_hParam": 24, "m_value": 26}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimActionUpdater"}, "CSingleFrameUpdateNode": {"fields": {"m_actions": 88, "m_flCycle": 120, "m_hPoseCacheHandle": 112, "m_hSequence": 116}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "CSlopeComponentUpdater": {"fields": {"m_flTraceDistance": 52, "m_hSlopeAngle": 56, "m_hSlopeAngleFront": 58, "m_hSlopeAngleSide": 60, "m_hSlopeHeading": 62, "m_hSlopeNormal": 64, "m_hSlopeNormal_WorldSpace": 66}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CSlowDownOnSlopesUpdateNode": {"fields": {"m_flSlowDownStrength": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CSolveIKChainUpdateNode": {"fields": {"m_opFixedData": 128, "m_targetHandles": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CSolveIKTargetHandle_t": {"fields": {"m_orientationHandle": 2, "m_positionHandle": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSpeedScaleUpdateNode": {"fields": {"m_paramIndex": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CStanceOverrideUpdateNode": {"fields": {"m_eMode": 148, "m_footStanceInfo": 104, "m_hParameter": 144, "m_pStanceSourceNode": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CStanceScaleUpdateNode": {"fields": {"m_hParam": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CStateActionUpdater": {"fields": {"m_eBehavior": 8, "m_pAction": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CStateMachineComponentUpdater": {"fields": {"m_stateMachine": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimComponentUpdater"}, "CStateMachineUpdateNode": {"fields": {"m_bBlockWaningTags": 244, "m_bLockStateWhenWaning": 245, "m_stateData": 192, "m_stateMachine": 104, "m_transitionData": 216}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CStateNodeStateData": {"fields": {"m_bExclusiveRootMotion": 0, "m_bExclusiveRootMotionFirstFrame": 0, "m_pChild": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CStateNodeTransitionData": {"fields": {"m_bReset": 0, "m_blendDuration": 8, "m_curve": 0, "m_resetCycleOption": 0, "m_resetCycleValue": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CStateUpdateData": {"fields": {"m_actions": 40, "m_bIsEndState": 0, "m_bIsPassthrough": 0, "m_bIsStartState": 0, "m_hScript": 8, "m_name": 0, "m_stateID": 64, "m_transitionIndices": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CStaticPoseCache": {"fields": {"m_nBoneCount": 40, "m_nMorphCount": 44, "m_poses": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CStaticPoseCacheBuilder": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CStaticPoseCache"}, "CStepsRemainingMetricEvaluator": {"fields": {"m_flMinStepsRemaining": 104, "m_footIndices": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CStopAtGoalUpdateNode": {"fields": {"m_damping": 128, "m_flInnerRadius": 112, "m_flMaxScale": 116, "m_flMinScale": 120, "m_flOuterRadius": 108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CStringAnimTag": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CSubtractUpdateNode": {"fields": {"m_bApplyChannelsSeparately": 145, "m_bApplyToFootMotion": 144, "m_bUseModelSpace": 146, "m_footMotionTiming": 140}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBinaryUpdateNode"}, "CSymbolAnimParameter": {"fields": {"m_defaultValue": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CTargetSelectorUpdateNode": {"fields": {"m_children": 88, "m_hFacePositionParameter": 118, "m_hPositionParameter": 116}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CTargetWarpUpdateNode": {"fields": {"m_hFacePositionParameter": 110, "m_hPositionParameter": 108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CTaskHandshakeAnimTag": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CHandshakeAnimTagBase"}, "CTaskStatusAnimTag": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CAnimTagBase"}, "CTiltTwistConstraint": {"fields": {"m_nSlaveAxis": 108, "m_nTargetAxis": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CTimeRemainingMetricEvaluator": {"fields": {"m_bFilterByTimeRemaining": 88, "m_bMatchByTimeRemaining": 80, "m_flMaxTimeRemaining": 84, "m_flMinTimeRemaining": 92}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CMotionMetricEvaluator"}, "CToggleComponentActionUpdater": {"fields": {"m_bSetEnabled": 28, "m_componentID": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimActionUpdater"}, "CTransitionUpdateData": {"fields": {"m_bDisabled": 0, "m_destStateIndex": 1, "m_nHandshakeMaskToDisableFirst": 0, "m_srcStateIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CTurnHelperUpdateNode": {"fields": {"m_bMatchChildDuration": 120, "m_bUseManualTurnOffset": 128, "m_facingTarget": 108, "m_manualTurnOffset": 124, "m_turnDuration": 116, "m_turnStartTimeOffset": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CTwistConstraint": {"fields": {"m_bInverse": 104, "m_qChildBindRotation": 128, "m_qParentBindRotation": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseConstraint"}, "CTwoBoneIKUpdateNode": {"fields": {"m_opFixedData": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CUnaryUpdateNode": {"fields": {"m_pChildNode": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimUpdateNodeBase"}, "CVPhysXSurfacePropertiesList": {"fields": {"m_surfacePropertiesList": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CVectorAnimParameter": {"fields": {"m_bInterpolate": 140, "m_defaultValue": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": "CConcreteAnimParameter"}, "CVectorQuantizer": {"fields": {"m_centroidVectors": 0, "m_nCentroids": 24, "m_nDimensions": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CVirtualAnimParameter": {"fields": {"m_eParamType": 120, "m_expressionString": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CAnimParameterBase"}, "CWayPointHelperUpdateNode": {"fields": {"m_bOnlyGoals": 116, "m_bPreventOvershoot": 117, "m_bPreventUndershoot": 118, "m_flEndCycle": 112, "m_flStartCycle": 108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CUnaryUpdateNode"}, "CZeroPoseUpdateNode": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CLeafUpdateNode"}, "ChainToSolveData_t": {"fields": {"m_DebugSetting": 56, "m_SolverSettings": 4, "m_TargetSettings": 16, "m_flDebugNormalizedValue": 60, "m_nChainIndex": 0, "m_vDebugOffset": 64}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ConfigIndex": {"fields": {"m_nConfig": 2, "m_nGroup": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "DynamicMeshDeformParams_t": {"fields": {"m_bComputeDynamicMeshTensionAfterAnimation": 9, "m_bRecomputeSmoothNormalsAfterAnimation": 8, "m_bSmoothNormalsAcrossUvSeams": 10, "m_flTensionCompressScale": 0, "m_flTensionStretchScale": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FollowAttachmentSettings_t": {"fields": {"m_attachment": 0, "m_bMatchRotation": 133, "m_bMatchTranslation": 132, "m_boneIndex": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FollowTargetOpFixedSettings_t": {"fields": {"m_bBoneTarget": 4, "m_bMatchTargetOrientation": 13, "m_bWorldCoodinateTarget": 12, "m_boneIndex": 0, "m_boneTargetIndex": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FootFixedData_t": {"fields": {"m_flMaxIKLength": 48, "m_flMaxRotationLeft": 60, "m_flMaxRotationRight": 64, "m_ikChainIndex": 44, "m_nAnkleBoneIndex": 36, "m_nFootIndex": 52, "m_nIKAnchorBoneIndex": 40, "m_nTagIndex": 56, "m_nTargetBoneIndex": 32, "m_vHeelOffset": 16, "m_vToeOffset": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FootFixedSettings": {"fields": {"m_bEnableTracing": 48, "m_flFootBaseLength": 32, "m_flMaxRotationLeft": 36, "m_flMaxRotationRight": 40, "m_flTraceAngleBlend": 52, "m_footstepLandedTagIndex": 44, "m_nDisableTagIndex": 56, "m_nFootIndex": 60, "m_traceSettings": 0, "m_vFootBaseBindPosePositionMS": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FootLockPoseOpFixedSettings": {"fields": {"m_bAlwaysUseFallbackHinge": 50, "m_bApplyFootRotationLimits": 51, "m_bApplyHipDrop": 49, "m_bApplyLegTwistLimits": 52, "m_bApplyTilt": 48, "m_bEnableLockBreaking": 68, "m_bEnableStretching": 80, "m_flExtensionScale": 60, "m_flLockBlendTime": 76, "m_flLockBreakTolerance": 72, "m_flMaxFootHeight": 56, "m_flMaxLegTwist": 64, "m_flMaxStretchAmount": 84, "m_flStretchExtensionScale": 88, "m_footInfo": 0, "m_hipDampingSettings": 24, "m_ikSolverType": 44, "m_nHipBoneIndex": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FootPinningPoseOpFixedData_t": {"fields": {"m_bApplyFootRotationLimits": 41, "m_bApplyLegTwistLimits": 40, "m_flBlendTime": 24, "m_flLockBreakDistance": 28, "m_flMaxLegTwist": 32, "m_footInfo": 0, "m_nHipBoneIndex": 36}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FootStepTrigger": {"fields": {"m_nFootIndex": 24, "m_tags": 0, "m_triggerPhase": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "HSequence": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "HitReactFixedSettings_t": {"fields": {"m_flCounterRotationScale": 20, "m_flDistanceFadeScale": 24, "m_flHipBoneTranslationScale": 52, "m_flHipDipDelay": 64, "m_flHipDipImpactScale": 60, "m_flHipDipSpringStrength": 56, "m_flMaxAngleRadians": 44, "m_flMaxImpactForce": 8, "m_flMinImpactForce": 12, "m_flPropagationScale": 28, "m_flSpringStrength": 36, "m_flWhipDelay": 32, "m_flWhipImpactScale": 16, "m_flWhipSpringStrength": 40, "m_nEffectedBoneCount": 4, "m_nHipBoneIndex": 48, "m_nWeightListIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "IKBoneNameAndIndex_t": {"fields": {"m_Name": 0}, "metadata": [], "parent": null}, "IKDemoCaptureSettings_t": {"fields": {"m_eMode": 8, "m_ikChainName": 16, "m_oneBoneEnd": 32, "m_oneBoneStart": 24, "m_parentBoneName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "IKSolverSettings_t": {"fields": {"m_EndEffectorRotationFixUpMode": 8, "m_SolverType": 0, "m_nNumIterations": 4}, "metadata": [], "parent": null}, "IKTargetSettings_t": {"fields": {"m_AnimgraphParameterNameOrientation": 28, "m_AnimgraphParameterNamePosition": 24, "m_Bone": 8, "m_TargetCoordSystem": 32, "m_TargetSource": 0}, "metadata": [], "parent": null}, "JiggleBoneSettingsList_t": {"fields": {"m_boneSettings": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "JiggleBoneSettings_t": {"fields": {"m_eSimSpace": 40, "m_flDamping": 12, "m_flMaxTimeStep": 8, "m_flSpringStrength": 4, "m_nBoneIndex": 0, "m_vBoundsMaxLS": 16, "m_vBoundsMinLS": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "LookAtBone_t": {"fields": {"m_index": 0, "m_weight": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "LookAtOpFixedSettings_t": {"fields": {"m_attachment": 0, "m_bMaintainUpDirection": 185, "m_bRotateYawForward": 184, "m_bTargetIsPosition": 186, "m_bUseHysteresis": 187, "m_bones": 144, "m_damping": 128, "m_flHysteresisInnerAngle": 176, "m_flHysteresisOuterAngle": 180, "m_flPitchLimit": 172, "m_flYawLimit": 168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MaterialGroup_t": {"fields": {"m_materials": 8, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ModelBoneFlexDriverControl_t": {"fields": {"m_flMax": 24, "m_flMin": 20, "m_flexController": 8, "m_flexControllerToken": 16, "m_nBoneComponent": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ModelBoneFlexDriver_t": {"fields": {"m_boneName": 0, "m_boneNameToken": 8, "m_controls": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ModelSkeletonData_t": {"fields": {"m_boneName": 0, "m_bonePosParent": 96, "m_boneRotParent": 120, "m_boneScaleParent": 144, "m_boneSphere": 48, "m_nFlag": 72, "m_nParent": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MoodAnimationLayer_t": {"fields": {"m_bActiveListening": 8, "m_bActiveTalking": 9, "m_bScaleWithInts": 56, "m_flDurationScale": 48, "m_flEndOffset": 76, "m_flFadeIn": 84, "m_flFadeOut": 88, "m_flIntensity": 40, "m_flNextStart": 60, "m_flStartOffset": 68, "m_layerAnimations": 16, "m_sName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyArrayElementName<PERSON>ey", "type": "Unknown"}], "parent": null}, "MoodAnimation_t": {"fields": {"m_flWeight": 8, "m_sName": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyArrayElementName<PERSON>ey", "type": "Unknown"}], "parent": null}, "MotionBlendItem": {"fields": {"m_flKeyValue": 8, "m_pChild": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MotionDBIndex": {"fields": {"m_nIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MotionIndex": {"fields": {"m_nGroup": 0, "m_nMotion": 2}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "NmCompressionSettings_t": {"fields": {"m_bIsRotationStatic": 48, "m_bIsScaleStatic": 50, "m_bIsTranslationStatic": 49, "m_constantRotation": 32, "m_scaleRange": 24, "m_translationRangeX": 0, "m_translationRangeY": 8, "m_translationRangeZ": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "NmCompressionSettings_t__QuantizationRange_t": {"fields": {"m_flRangeLength": 4, "m_flRangeStart": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "NmPercent_t": {"fields": {"m_flValue": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "NmSyncTrackTimeRange_t": {"fields": {"m_endTime": 8, "m_startTime": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "NmSyncTrackTime_t": {"fields": {"m_nEventIdx": 0, "m_percentageThrough": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParamSpanSample_t": {"fields": {"m_flCycle": 20, "m_value": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParamSpan_t": {"fields": {"m_eParamType": 26, "m_flEndCycle": 32, "m_flStartCycle": 28, "m_hParam": 24, "m_samples": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PermModelDataAnimatedMaterialAttribute_t": {"fields": {"m_AttributeName": 0, "m_nNumChannels": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PermModelData_t": {"fields": {"m_AnimatedMaterialAttributes": 688, "m_BodyGroupsHiddenInTools": 640, "m_ExtParts": 96, "m_boneFlexDrivers": 608, "m_lodGroupSwitchDistances": 216, "m_materialGroups": 360, "m_meshGroups": 336, "m_modelInfo": 8, "m_modelSkeleton": 392, "m_nDefaultMeshGroupMask": 384, "m_name": 0, "m_pModelConfigList": 632, "m_refAnimGroups": 288, "m_refAnimIncludeModels": 664, "m_refLODGroupMasks": 192, "m_refMeshGroupMasks": 144, "m_refMeshes": 120, "m_refPhysGroupMasks": 168, "m_refPhysicsData": 240, "m_refPhysicsHitboxData": 264, "m_refSequenceGroups": 312, "m_remappingTable": 560, "m_remappingTableStarts": 584}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PermModelExtPart_t": {"fields": {"m_Name": 32, "m_Transform": 0, "m_nParent": 40, "m_refModel": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PermModelInfo_t": {"fields": {"m_flMass": 52, "m_flMaxEyeDeflection": 68, "m_keyValueText": 80, "m_nFlags": 0, "m_sSurfaceProperty": 72, "m_vEyePosition": 56, "m_vHullMax": 16, "m_vHullMin": 4, "m_vViewMax": 40, "m_vViewMin": 28}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PhysSoftbodyDesc_t": {"fields": {"m_Capsules": 72, "m_InitPose": 96, "m_ParticleBoneHash": 0, "m_ParticleBoneName": 120, "m_Particles": 24, "m_Springs": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "RenderHairStrandInfo_t": {"fields": {"m_nGuideHairIndices_nSurfaceTriIndex": 0, "m_nPackedBaseUv": 24, "m_nPackedSurfaceNormalOs": 28, "m_nPackedSurfaceTangentOs": 32, "m_vGuideBary_vBaseBary": 8, "m_vRootOffset_flLengthScale": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "RenderSkeletonBone_t": {"fields": {"m_bbox": 64, "m_boneName": 0, "m_flSphereRadius": 88, "m_invBindPose": 16, "m_parentName": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SampleCode": {"fields": {"m_subCode": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ScriptInfo_t": {"fields": {"m_code": 0, "m_eScriptType": 80, "m_paramsModified": 8, "m_proxyReadParams": 32, "m_proxyWriteParams": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonAnimCapture_t": {"fields": {"m_CaptureName": 40, "m_FeModelInitPose": 72, "m_Frames": 168, "m_ImportedCollision": 8, "m_ModelBindPose": 48, "m_ModelName": 32, "m_bPredicted": 100, "m_nEntIndex": 0, "m_nEntParent": 4, "m_nFlexControllers": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonAnimCapture_t__Bone_t": {"fields": {"m_BindPose": 16, "m_Name": 0, "m_nParent": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonAnimCapture_t__Camera_t": {"fields": {"m_flTime": 32, "m_tmCamera": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonAnimCapture_t__FrameStamp_t": {"fields": {"m_bPredicted": 9, "m_bTeleportTick": 8, "m_flCurTime": 12, "m_flEntitySimTime": 4, "m_flRealTime": 16, "m_flTime": 0, "m_nFrameCount": 20, "m_nTickCount": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonAnimCapture_t__Frame_t": {"fields": {"m_CompositeBones": 72, "m_FeModelAnims": 120, "m_FeModelPos": 144, "m_FlexControllerWeights": 168, "m_SimStateBones": 96, "m_Stamp": 4, "m_Transform": 32, "m_bTeleport": 64, "m_flTime": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonBoneBounds_t": {"fields": {"m_vecCenter": 0, "m_vecSize": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SkeletonDemoDb_t": {"fields": {"m_AnimCaptures": 0, "m_CameraTrack": 24, "m_flRecordingTime": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SolveIKChainPoseOpFixedSettings_t": {"fields": {"m_ChainsToSolveData": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "StanceInfo_t": {"fields": {"m_flDirection": 12, "m_vPosition": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TagSpan_t": {"fields": {"m_endCycle": 8, "m_startCycle": 4, "m_tagIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TraceSettings_t": {"fields": {"m_flTraceHeight": 0, "m_flTraceRadius": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TwoBoneIKSettings_t": {"fields": {"m_bAlwaysUseFallbackHinge": 296, "m_bConstrainTwist": 333, "m_bMatchTargetOrientation": 332, "m_endEffectorAttachment": 16, "m_endEffectorType": 0, "m_flMaxTwist": 336, "m_hPositionParam": 292, "m_hRotationParam": 294, "m_nEndBoneIndex": 328, "m_nFixedBoneIndex": 320, "m_nMiddleBoneIndex": 324, "m_targetAttachment": 160, "m_targetBoneIndex": 288, "m_targetType": 144, "m_vLsFallbackHingeAxis": 304}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXAggregateData_t": {"fields": {"m_bindPose": 104, "m_boneNames": 32, "m_boneParents": 208, "m_bonesHash": 8, "m_collisionAttributes": 256, "m_constraints2": 152, "m_debugPartNames": 280, "m_embeddedKeyvalues": 304, "m_indexHash": 80, "m_indexNames": 56, "m_joints": 176, "m_nFlags": 0, "m_nRefCounter": 2, "m_pFeModel": 200, "m_parts": 128, "m_surfacePropertyHashes": 232}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXBodyPart_t": {"fields": {"m_bOverrideMassCenter": 144, "m_flAngularDamping": 140, "m_flInertiaScale": 132, "m_flLinearDamping": 136, "m_flMass": 4, "m_nCollisionAttributeIndex": 128, "m_nFlags": 0, "m_nReserved": 130, "m_rnShape": 8, "m_vMassCenterOverride": 148}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXCollisionAttributes_t": {"fields": {"m_CollisionGroup": 0, "m_CollisionGroupString": 80, "m_InteractAs": 8, "m_InteractAsStrings": 88, "m_InteractExclude": 56, "m_InteractExcludeStrings": 136, "m_InteractWith": 32, "m_InteractWithStrings": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXConstraint2_t": {"fields": {"m_nChild": 6, "m_nFlags": 0, "m_nParent": 4, "m_params": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXConstraintParams_t": {"fields": {"m_anchor": 4, "m_axes": 28, "m_driveDampingSlerp": 232, "m_driveDampingSwing": 228, "m_driveDampingTwist": 224, "m_driveDampingX": 200, "m_driveDampingY": 204, "m_driveDampingZ": 208, "m_driveSpringSlerp": 220, "m_driveSpringSwing": 216, "m_driveSpringTwist": 212, "m_driveSpringX": 188, "m_driveSpringY": 192, "m_driveSpringZ": 196, "m_goalAngularVelocity": 176, "m_goalOrientation": 160, "m_goalPosition": 148, "m_linearLimitDamping": 80, "m_linearLimitRestitution": 72, "m_linearLimitSpring": 76, "m_linearLimitValue": 68, "m_maxForce": 60, "m_maxTorque": 64, "m_nFlags": 3, "m_nRotateMotion": 2, "m_nTranslateMotion": 1, "m_nType": 0, "m_projectionAngularTolerance": 244, "m_projectionLinearTolerance": 240, "m_solverIterationCount": 236, "m_swing1LimitDamping": 128, "m_swing1LimitRestitution": 120, "m_swing1LimitSpring": 124, "m_swing1LimitValue": 116, "m_swing2LimitDamping": 144, "m_swing2LimitRestitution": 136, "m_swing2LimitSpring": 140, "m_swing2LimitValue": 132, "m_twistHighLimitDamping": 112, "m_twistHighLimitRestitution": 104, "m_twistHighLimitSpring": 108, "m_twistHighLimitValue": 100, "m_twistLowLimitDamping": 96, "m_twistLowLimitRestitution": 88, "m_twistLowLimitSpring": 92, "m_twistLowLimitValue": 84}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXJoint_t": {"fields": {"m_Frame1": 16, "m_Frame2": 48, "m_LinearLimit": 84, "m_SwingLimit": 116, "m_TwistLimit": 128, "m_bEnableAngularMotor": 136, "m_bEnableCollision": 80, "m_bEnableLinearLimit": 81, "m_bEnableLinearMotor": 92, "m_bEnableSwingLimit": 112, "m_bEnableTwistLimit": 124, "m_flAngularDampingRatio": 168, "m_flAngularFrequency": 164, "m_flElasticDamping": 180, "m_flElasticity": 176, "m_flFriction": 172, "m_flLinearDampingRatio": 160, "m_flLinearFrequency": 156, "m_flMaxForce": 108, "m_flMaxTorque": 152, "m_flPlasticity": 184, "m_nBody1": 2, "m_nBody2": 4, "m_nFlags": 6, "m_nType": 0, "m_vAngularTargetVelocity": 140, "m_vLinearTargetVelocity": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysXRange_t": {"fields": {"m_flMax": 4, "m_flMin": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VPhysics2ShapeDef_t": {"fields": {"m_CollisionAttributeIndices": 96, "m_capsules": 24, "m_hulls": 48, "m_meshes": 72, "m_spheres": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "WeightList": {"fields": {"m_name": 0, "m_weights": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}}, "enums": {"AimMatrixBlendMode": {"alignment": 4, "members": {"AimMatrixBlendMode_Additive": 1, "AimMatrixBlendMode_BoneMask": 3, "AimMatrixBlendMode_ModelSpaceAdditive": 2, "AimMatrixBlendMode_None": 0}, "type": "uint32"}, "AnimNodeNetworkMode": {"alignment": 4, "members": {"ClientSimulate": 1, "ServerAuthoritative": 0}, "type": "uint32"}, "AnimParamButton_t": {"alignment": 4, "members": {"ANIMPARAM_BUTTON_A": 5, "ANIMPARAM_BUTTON_B": 6, "ANIMPARAM_BUTTON_DPAD_DOWN": 3, "ANIMPARAM_BUTTON_DPAD_LEFT": 4, "ANIMPARAM_BUTTON_DPAD_RIGHT": 2, "ANIMPARAM_BUTTON_DPAD_UP": 1, "ANIMPARAM_BUTTON_LEFT_SHOULDER": 9, "ANIMPARAM_BUTTON_LTRIGGER": 11, "ANIMPARAM_BUTTON_NONE": 0, "ANIMPARAM_BUTTON_RIGHT_SHOULDER": 10, "ANIMPARAM_BUTTON_RTRIGGER": 12, "ANIMPARAM_BUTTON_X": 7, "ANIMPARAM_BUTTON_Y": 8}, "type": "uint32"}, "AnimParamNetworkSetting": {"alignment": 4, "members": {"AlwaysNetwork": 1, "Auto": 0, "NeverNetwork": 2}, "type": "uint32"}, "AnimParamType_t": {"alignment": 1, "members": {"ANIMPARAM_BOOL": 1, "ANIMPARAM_COUNT": 8, "ANIMPARAM_ENUM": 2, "ANIMPARAM_FLOAT": 4, "ANIMPARAM_GLOBALSYMBOL": 7, "ANIMPARAM_INT": 3, "ANIMPARAM_QUATERNION": 6, "ANIMPARAM_UNKNOWN": 0, "ANIMPARAM_VECTOR": 5}, "type": "uint8"}, "AnimPoseControl": {"alignment": 4, "members": {"AbsolutePoseControl": 1, "NoPoseControl": 0, "RelativePoseControl": 2}, "type": "uint32"}, "AnimScriptType": {"alignment": 2, "members": {"ANIMSCRIPT_FUSE_GENERAL": 0, "ANIMSCRIPT_FUSE_STATEMACHINE": 1, "ANIMSCRIPT_TYPE_INVALID": -1}, "type": "uint16"}, "AnimValueSource": {"alignment": 4, "members": {"AccelerationFrontBack": 22, "AccelerationHeading": 14, "AccelerationLeftRight": 21, "AccelerationSpeed": 15, "BoundaryRadius": 11, "FacingHeading": 4, "FingerCurl_Index": 28, "FingerCurl_Middle": 29, "FingerCurl_Pinky": 31, "FingerCurl_Ring": 30, "FingerCurl_Thumb": 27, "FingerSplay_Index_Middle": 33, "FingerSplay_Middle_Ring": 34, "FingerSplay_Ring_Pinky": 35, "FingerSplay_Thumb_Index": 32, "ForwardSpeed": 2, "GoalDistance": 20, "LookDistance": 7, "LookHeading": 5, "LookPitch": 6, "MaxMoveSpeed": 26, "MoveHeading": 0, "MoveHeadingRelativeToLookHeading": 25, "MoveSpeed": 1, "Parameter": 8, "RootMotionSpeed": 23, "RootMotionTurnSpeed": 24, "SlopeAngle": 17, "SlopeHeading": 16, "SlopePitch": 18, "SlopeYaw": 19, "StrafeSpeed": 3, "TargetMoveHeading": 12, "TargetMoveSpeed": 13, "WayPointDistance": 10, "WayPointHeading": 9}, "type": "uint32"}, "AnimVectorSource": {"alignment": 4, "members": {"Acceleration": 5, "FacingPosition": 1, "GoalPosition": 11, "LookDirection": 2, "LookTarget": 8, "LookTarget_WorldSpace": 9, "ManualTarget_WorldSpace": 13, "MoveDirection": 0, "RootMotionVelocity": 12, "SlopeNormal": 6, "SlopeNormal_WorldSpace": 7, "VectorParameter": 3, "WayPointDirection": 4, "WayPointPosition": 10}, "type": "uint32"}, "AnimationProcessingType_t": {"alignment": 4, "members": {"ANIMATION_PROCESSING_CLIENT_INTERPOLATION": 3, "ANIMATION_PROCESSING_CLIENT_PREDICTION": 2, "ANIMATION_PROCESSING_CLIENT_RENDER": 4, "ANIMATION_PROCESSING_CLIENT_SIMULATION": 1, "ANIMATION_PROCESSING_MAX": 5, "ANIMATION_PROCESSING_SERVER_SIMULATION": 0}, "type": "uint32"}, "AnimationSnapshotType_t": {"alignment": 4, "members": {"ANIMATION_SNAPSHOT_CLIENT_INTERPOLATION": 3, "ANIMATION_SNAPSHOT_CLIENT_PREDICTION": 2, "ANIMATION_SNAPSHOT_CLIENT_RENDER": 4, "ANIMATION_SNAPSHOT_CLIENT_SIMULATION": 1, "ANIMATION_SNAPSHOT_FINAL_COMPOSITE": 5, "ANIMATION_SNAPSHOT_MAX": 6, "ANIMATION_SNAPSHOT_SERVER_SIMULATION": 0}, "type": "uint32"}, "BinaryNodeChildOption": {"alignment": 4, "members": {"Child1": 0, "Child2": 1}, "type": "uint32"}, "BinaryNodeTiming": {"alignment": 4, "members": {"SyncChildren": 2, "UseChild1": 0, "UseChild2": 1}, "type": "uint32"}, "Blend2DMode": {"alignment": 4, "members": {"Blend2DMode_Directional": 1, "Blend2DMode_General": 0}, "type": "uint32"}, "BlendKeyType": {"alignment": 4, "members": {"BlendKey_Distance": 2, "BlendKey_RemainingDistance": 3, "BlendKey_UserValue": 0, "BlendKey_Velocity": 1}, "type": "uint32"}, "BoneMaskBlendSpace": {"alignment": 4, "members": {"BlendSpace_Model": 1, "BlendSpace_Model_RotationOnly": 2, "BlendSpace_Model_TranslationOnly": 3, "BlendSpace_Parent": 0}, "type": "uint32"}, "BoneTransformSpace_t": {"alignment": 4, "members": {"BoneTransformSpace_Invalid": -1, "BoneTransformSpace_Model": 1, "BoneTransformSpace_Parent": 0, "BoneTransformSpace_World": 2}, "type": "uint32"}, "CAnimationGraphVisualizerPrimitiveType": {"alignment": 4, "members": {"ANIMATIONGRAPHVISUALIZERPRIMITIVETYPE_Axis": 4, "ANIMATIONGRAPHVISUALIZERPRIMITIVETYPE_Line": 2, "ANIMATIONGRAPHVISUALIZERPRIMITIVETYPE_Pie": 3, "ANIMATIONGRAPHVISUALIZERPRIMITIVETYPE_Sphere": 1, "ANIMATIONGRAPHVISUALIZERPRIMITIVETYPE_Text": 0}, "type": "uint32"}, "CNmBoneMask__WeightInfo_t": {"alignment": 1, "members": {"Mixed": 1, "One": 2, "Zero": 0}, "type": "uint8"}, "CNmFloatAngleMathNode__Operation_t": {"alignment": 1, "members": {"ClampTo180": 0, "ClampTo360": 1, "FlipHemisphere": 2, "FlipHemisphereNegate": 3}, "type": "uint8"}, "CNmFloatComparisonNode__Comparison_t": {"alignment": 1, "members": {"GreaterThan": 3, "GreaterThanEqual": 0, "LessThan": 4, "LessThanEqual": 1, "NearEqual": 2}, "type": "uint8"}, "CNmFloatMathNode__Operator_t": {"alignment": 1, "members": {"Add": 0, "Div": 3, "Mul": 2, "Sub": 1}, "type": "uint8"}, "CNmIDComparisonNode__Comparison_t": {"alignment": 1, "members": {"DoesntMatch": 1, "Matches": 0}, "type": "uint8"}, "CNmRootMotionData__SamplingMode_t": {"alignment": 1, "members": {"Delta": 0, "WorldSpace": 1}, "type": "uint8"}, "CNmRootMotionOverrideNode__OverrideFlags_t": {"alignment": 1, "members": {"AllowFacingPitch": 3, "AllowMoveX": 0, "AllowMoveY": 1, "AllowMoveZ": 2, "ListenForEvents": 4}, "type": "uint8"}, "CNmSyncEventIndexConditionNode__TriggerMode_t": {"alignment": 1, "members": {"ExactlyAtEventIndex": 0, "GreaterThanEqualToEventIndex": 1}, "type": "uint8"}, "CNmTargetInfoNode__Info_t": {"alignment": 4, "members": {"AngleHorizontal": 0, "AngleVertical": 1, "DeltaOrientationX": 5, "DeltaOrientationY": 6, "DeltaOrientationZ": 7, "Distance": 2, "DistanceHorizontalOnly": 3, "DistanceVerticalOnly": 4}, "type": "uint32"}, "CNmTimeConditionNode__ComparisonType_t": {"alignment": 1, "members": {"ElapsedTime": 2, "LoopCount": 3, "PercentageThroughState": 0, "PercentageThroughSyncEvent": 1}, "type": "uint8"}, "CNmTimeConditionNode__Operator_t": {"alignment": 1, "members": {"GreaterThan": 2, "GreaterThanEqual": 3, "LessThan": 0, "LessThanEqual": 1}, "type": "uint8"}, "CNmTransitionNode__TransitionOptions_t": {"alignment": 1, "members": {"ClampDuration": 1, "MatchSourceTime": 3, "MatchSyncEventID": 5, "MatchSyncEventIndex": 4, "MatchSyncEventPercentage": 6, "None": 0, "PreferClosestSyncEventID": 7, "Synchronized": 2}, "type": "uint8"}, "CNmVectorInfoNode__Info_t": {"alignment": 1, "members": {"AngleHorizontal": 4, "AngleVertical": 5, "Length": 3, "X": 0, "Y": 1, "Z": 2}, "type": "uint8"}, "ChoiceBlendMethod": {"alignment": 4, "members": {"PerChoiceBlendTimes": 1, "SingleBlendTime": 0}, "type": "uint32"}, "ChoiceChangeMethod": {"alignment": 4, "members": {"OnCycleEnd": 1, "OnReset": 0, "OnResetOrCycleEnd": 2}, "type": "uint32"}, "ChoiceMethod": {"alignment": 4, "members": {"Iterate": 2, "IterateRandom": 3, "WeightedRandom": 0, "WeightedRandomNoRepeat": 1}, "type": "uint32"}, "DampingSpeedFunction": {"alignment": 4, "members": {"Constant": 1, "NoDamping": 0, "Spring": 2}, "type": "uint32"}, "EDemoBoneSelectionMode": {"alignment": 4, "members": {"CaptureAllBones": 0, "CaptureSelectedBones": 1}, "type": "uint32"}, "EIKEndEffectorRotationFixUpMode": {"alignment": 4, "members": {"Count": 4, "LookAtTargetForward": 2, "MaintainParentOrientation": 3, "MatchTargetOrientation": 1, "None": 0}, "type": "uint32"}, "FacingMode": {"alignment": 1, "members": {"FacingMode_Invalid": 0, "FacingMode_LookTarget": 3, "FacingMode_Manual": 1, "FacingMode_ManualPosition": 4, "FacingMode_Path": 2}, "type": "uint8"}, "FieldNetworkOption": {"alignment": 4, "members": {"Auto": 0, "ForceDisable": 2, "ForceEnable": 1}, "type": "uint32"}, "FlexOpCode_t": {"alignment": 4, "members": {"FLEX_OP_2WAY_0": 15, "FLEX_OP_2WAY_1": 16, "FLEX_OP_ABS": 26, "FLEX_OP_ADD": 4, "FLEX_OP_CLOSE": 11, "FLEX_OP_COMBO": 18, "FLEX_OP_COMMA": 12, "FLEX_OP_CONST": 1, "FLEX_OP_COS": 25, "FLEX_OP_DIV": 7, "FLEX_OP_DME_LOWER_EYELID": 20, "FLEX_OP_DME_UPPER_EYELID": 21, "FLEX_OP_DOMINATE": 19, "FLEX_OP_EXP": 9, "FLEX_OP_FETCH1": 2, "FLEX_OP_FETCH2": 3, "FLEX_OP_MAX": 13, "FLEX_OP_MIN": 14, "FLEX_OP_MUL": 6, "FLEX_OP_NEG": 8, "FLEX_OP_NWAY": 17, "FLEX_OP_OPEN": 10, "FLEX_OP_REMAPVALCLAMPED": 23, "FLEX_OP_SIN": 24, "FLEX_OP_SQRT": 22, "FLEX_OP_SUB": 5}, "type": "uint32"}, "FootFallTagFoot_t": {"alignment": 4, "members": {"FOOT1": 0, "FOOT2": 1, "FOOT3": 2, "FOOT4": 3, "FOOT5": 4, "FOOT6": 5, "FOOT7": 6, "FOOT8": 7}, "type": "uint32"}, "FootLockSubVisualization": {"alignment": 4, "members": {"FOOTLOCKSUBVISUALIZATION_IKSolve": 1, "FOOTLOCKSUBVISUALIZATION_ReachabilityAnalysis": 0}, "type": "uint32"}, "FootPinningTimingSource": {"alignment": 4, "members": {"FootMotion": 0, "Parameter": 2, "Tag": 1}, "type": "uint32"}, "FootstepLandedFootSoundType_t": {"alignment": 4, "members": {"FOOTSOUND_Left": 0, "FOOTSOUND_Right": 1, "FOOTSOUND_UseOverrideSound": 2}, "type": "uint32"}, "HandshakeTagType_t": {"alignment": 4, "members": {"eCount": 2, "eInvalid": -1, "eMovement": 1, "eTask": 0}, "type": "uint32"}, "IKChannelMode": {"alignment": 4, "members": {"OneBone": 2, "OneBone_Translate": 3, "TwoBone": 0, "TwoBone_Translate": 1}, "type": "uint32"}, "IKSolverType": {"alignment": 4, "members": {"IKSOLVER_CCD": 4, "IKSOLVER_COUNT": 5, "IKSOLVER_DogLeg3Bone": 3, "IKSOLVER_Fabrik": 2, "IKSOLVER_Perlin": 0, "IKSOLVER_TwoBone": 1}, "type": "uint32"}, "IKTargetCoordinateSystem": {"alignment": 4, "members": {"IKTARGETCOORDINATESYSTEM_COUNT": 2, "IKTARGETCOORDINATESYSTEM_ModelSpace": 1, "IKTARGETCOORDINATESYSTEM_WorldSpace": 0}, "type": "uint32"}, "IKTargetSource": {"alignment": 4, "members": {"IKTARGETSOURCE_AnimgraphParameter": 1, "IKTARGETSOURCE_Bone": 0, "IKTARGETSOURCE_COUNT": 2}, "type": "uint32"}, "IkEndEffectorType": {"alignment": 4, "members": {"IkEndEffector_Attachment": 0, "IkEndEffector_Bone": 1}, "type": "uint32"}, "IkTargetType": {"alignment": 4, "members": {"IkTarget_Attachment": 0, "IkTarget_Bone": 1, "IkTarget_Parameter_ModelSpace": 2, "IkTarget_Parameter_WorldSpace": 3}, "type": "uint32"}, "JiggleBoneSimSpace": {"alignment": 4, "members": {"SimSpace_Local": 0, "SimSpace_Model": 1, "SimSpace_World": 2}, "type": "uint32"}, "JumpCorrectionMethod": {"alignment": 4, "members": {"AddCorrectionDelta": 1, "ScaleMotion": 0}, "type": "uint32"}, "MatterialAttributeTagType_t": {"alignment": 4, "members": {"MATERIAL_ATTRIBUTE_TAG_COLOR": 1, "MATERIAL_ATTRIBUTE_TAG_VALUE": 0}, "type": "uint32"}, "MeshDrawPrimitiveFlags_t": {"alignment": 4, "members": {"MESH_DRAW_FLAGS_CAN_BATCH_WITH_DYNAMIC_SHADER_CONSTANTS": 64, "MESH_DRAW_FLAGS_DRAW_LAST": 128, "MESH_DRAW_FLAGS_NONE": 0, "MESH_DRAW_FLAGS_USE_COMPRESSED_NORMAL_TANGENT": 2, "MESH_DRAW_FLAGS_USE_COMPRESSED_PER_VERTEX_LIGHTING": 16, "MESH_DRAW_FLAGS_USE_SHADOW_FAST_PATH": 1, "MESH_DRAW_FLAGS_USE_UNCOMPRESSED_PER_VERTEX_LIGHTING": 32, "MESH_DRAW_INPUT_LAYOUT_IS_NOT_MATCHED_TO_MATERIAL": 8}, "type": "uint32"}, "ModelBoneFlexComponent_t": {"alignment": 4, "members": {"MODEL_BONE_FLEX_INVALID": -1, "MODEL_BONE_FLEX_TX": 0, "MODEL_BONE_FLEX_TY": 1, "MODEL_BONE_FLEX_TZ": 2}, "type": "uint32"}, "ModelConfigAttachmentType_t": {"alignment": 4, "members": {"MODEL_CONFIG_ATTACHMENT_BONEMERGE": 2, "MODEL_CONFIG_ATTACHMENT_BONE_OR_ATTACHMENT": 0, "MODEL_CONFIG_ATTACHMENT_COUNT": 3, "MODEL_CONFIG_ATTACHMENT_INVALID": -1, "MODEL_CONFIG_ATTACHMENT_ROOT_RELATIVE": 1}, "type": "uint32"}, "ModelSkeletonData_t__BoneFlags_t": {"alignment": 4, "members": {"BLEND_PREALIGNED": 1048576, "FLAG_ALL_BONE_FLAGS": 1048575, "FLAG_ANIMATION": 64, "FLAG_ATTACHMENT": 32, "FLAG_BONEFLEXDRIVER": 4, "FLAG_BONE_MERGE_READ": 262144, "FLAG_BONE_MERGE_WRITE": 524288, "FLAG_BONE_USED_BY_VERTEX_LOD0": 1024, "FLAG_BONE_USED_BY_VERTEX_LOD1": 2048, "FLAG_BONE_USED_BY_VERTEX_LOD2": 4096, "FLAG_BONE_USED_BY_VERTEX_LOD3": 8192, "FLAG_BONE_USED_BY_VERTEX_LOD4": 16384, "FLAG_BONE_USED_BY_VERTEX_LOD5": 32768, "FLAG_BONE_USED_BY_VERTEX_LOD6": 65536, "FLAG_BONE_USED_BY_VERTEX_LOD7": 131072, "FLAG_CLOTH": 8, "FLAG_HITBOX": 256, "FLAG_MESH": 128, "FLAG_NO_BONE_FLAGS": 0, "FLAG_PHYSICS": 16, "FLAG_PROCEDURAL": 4194304, "FLAG_RIGIDLENGTH": 2097152}, "type": "uint32"}, "MoodType_t": {"alignment": 4, "members": {"eMoodType_Body": 1, "eMoodType_Head": 0}, "type": "uint32"}, "MorphBundleType_t": {"alignment": 4, "members": {"MORPH_BUNDLE_TYPE_COUNT": 3, "MORPH_BUNDLE_TYPE_NONE": 0, "MORPH_BUNDLE_TYPE_NORMAL_WRINKLE": 2, "MORPH_BUNDLE_TYPE_POSITION_SPEED": 1}, "type": "uint32"}, "MorphFlexControllerRemapType_t": {"alignment": 4, "members": {"MORPH_FLEXCONTROLLER_REMAP_2WAY": 1, "MORPH_FLEXCONTROLLER_REMAP_EYELID": 3, "MORPH_FLEXCONTROLLER_REMAP_NWAY": 2, "MORPH_FLEXCONTROLLER_REMAP_PASSTHRU": 0}, "type": "uint32"}, "MovementGait_t": {"alignment": 1, "members": {"eCount": 4, "eFast": 2, "eInvalid": -1, "eMedium": 1, "eSlow": 0, "eVeryFast": 3}, "type": "uint8"}, "NmCachedValueMode_t": {"alignment": 4, "members": {"OnEntry": 0, "OnExit": 1}, "type": "uint32"}, "NmEasingFunction_t": {"alignment": 1, "members": {"Back": 8, "Circ": 7, "Cubic": 2, "Expo": 6, "Linear": 0, "Quad": 1, "Quart": 3, "Quint": 4, "Sine": 5}, "type": "uint8"}, "NmEasingOperation_t": {"alignment": 1, "members": {"InCirc": 19, "InCubic": 4, "InExpo": 16, "InOutCirc": 21, "InOutCubic": 6, "InOutExpo": 18, "InOutQuad": 3, "InOutQuart": 9, "InOutQuint": 12, "InOutSine": 15, "InQuad": 1, "InQuart": 7, "InQuint": 10, "InSine": 13, "Linear": 0, "None": 22, "OutCirc": 20, "OutCubic": 5, "OutExpo": 17, "OutQuad": 2, "OutQuart": 8, "OutQuint": 11, "OutSine": 14}, "type": "uint8"}, "NmEventConditionRules_t": {"alignment": 1, "members": {"IgnoreInactiveEvents": 1, "LimitSearchToSourceState": 0, "OperatorAnd": 5, "OperatorOr": 4, "PreferHighestProgress": 3, "PreferHighestWeight": 2, "SearchBothStateAndAnimEvents": 8, "SearchOnlyAnimEvents": 7, "SearchOnlyStateEvents": 6}, "type": "uint8"}, "NmFootPhaseCondition_t": {"alignment": 1, "members": {"LeftFootDown": 0, "LeftFootPassing": 1, "LeftPhase": 4, "RightFootDown": 2, "RightFootPassing": 3, "RightPhase": 5}, "type": "uint8"}, "NmFootPhase_t": {"alignment": 1, "members": {"LeftFootDown": 0, "LeftFootPassing": 3, "RightFootDown": 2, "RightFootPassing": 1}, "type": "uint8"}, "NmFrameSnapEventMode_t": {"alignment": 4, "members": {"Floor": 0, "Round": 1}, "type": "uint32"}, "NmGraphValueType_t": {"alignment": 1, "members": {"BoneMask": 6, "Bool": 1, "Float": 3, "ID": 2, "Pose": 7, "Special": 8, "Target": 5, "Unknown": 0, "Vector": 4}, "type": "uint8"}, "NmPoseBlendMode_t": {"alignment": 1, "members": {"Additive": 1, "ModelSpace": 2, "Overlay": 0}, "type": "uint8"}, "NmRootMotionBlendMode_t": {"alignment": 1, "members": {"Additive": 1, "Blend": 0, "IgnoreSource": 2, "IgnoreTarget": 3}, "type": "uint8"}, "NmStateEventTypeCondition_t": {"alignment": 1, "members": {"Any": 4, "Entry": 0, "Exit": 2, "FullyInState": 1, "Timed": 3}, "type": "uint8"}, "NmTargetWarpAlgorithm_t": {"alignment": 1, "members": {"Bezier": 3, "Hermite": 1, "HermiteFeaturePreserving": 2, "Lerp": 0}, "type": "uint8"}, "NmTargetWarpRule_t": {"alignment": 1, "members": {"RotationOnly": 3, "WarpXY": 0, "WarpXYZ": 2, "WarpZ": 1}, "type": "uint8"}, "NmTransitionRuleCondition_t": {"alignment": 1, "members": {"AnyAllowed": 0, "Blocked": 3, "ConditionallyAllowed": 2, "FullyAllowed": 1}, "type": "uint8"}, "NmTransitionRule_t": {"alignment": 1, "members": {"AllowTransition": 0, "BlockTransition": 2, "ConditionallyAllowTransition": 1}, "type": "uint8"}, "ParticleAttachment_t": {"alignment": 4, "members": {"MAX_PATTACH_TYPES": 16, "PATTACH_ABSORIGIN": 0, "PATTACH_ABSORIGIN_FOLLOW": 1, "PATTACH_CENTER_FOLLOW": 13, "PATTACH_CUSTOMORIGIN": 2, "PATTACH_CUSTOMORIGIN_FOLLOW": 3, "PATTACH_CUSTOM_GAME_STATE_1": 14, "PATTACH_EYES_FOLLOW": 6, "PATTACH_HEALTHBAR": 15, "PATTACH_INVALID": -1, "PATTACH_MAIN_VIEW": 11, "PATTACH_OVERHEAD_FOLLOW": 7, "PATTACH_POINT": 4, "PATTACH_POINT_FOLLOW": 5, "PATTACH_RENDERORIGIN_FOLLOW": 10, "PATTACH_ROOTBONE_FOLLOW": 9, "PATTACH_WATERWAKE": 12, "PATTACH_WORLDORIGIN": 8}, "type": "uint32"}, "PermModelInfo_t__FlagEnum": {"alignment": 4, "members": {"FLAG_ANIMATION_DRIVEN_FLEXES": 2097152, "FLAG_DO_NOT_CAST_SHADOWS": 131072, "FLAG_FORCE_PHONEME_CROSSFADE": 4096, "FLAG_HAS_SKINNED_MESHES": 1024, "FLAG_IMPLICIT_BIND_POSE_SEQUENCE": 4194304, "FLAG_MODEL_DOC": 8388608, "FLAG_MODEL_IS_RUNTIME_COMBINED": 4, "FLAG_MODEL_PART_CHILD": 16, "FLAG_NAV_GEN_HULL": 64, "FLAG_NAV_GEN_NONE": 32, "FLAG_NO_ANIM_EVENTS": 1048576, "FLAG_NO_FORCED_FADE": 2048, "FLAG_SOURCE1_IMPORT": 8, "FLAG_TRANSLUCENT": 1, "FLAG_TRANSLUCENT_TWO_PASS": 2}, "type": "uint32"}, "PoseType_t": {"alignment": 1, "members": {"POSETYPE_DYNAMIC": 1, "POSETYPE_INVALID": 255, "POSETYPE_STATIC": 0}, "type": "uint8"}, "RagdollPoseControl": {"alignment": 4, "members": {"Absolute": 0, "Relative": 1}, "type": "uint32"}, "ResetCycleOption": {"alignment": 4, "members": {"Beginning": 0, "FixedValue": 3, "InverseSourceCycle": 2, "SameCycleAsSource": 1, "SameTimeAsSource": 4}, "type": "uint32"}, "SelectorTagBehavior_t": {"alignment": 4, "members": {"SelectorTagBehavior_OffBeforeFinished": 2, "SelectorTagBehavior_OffWhenFinished": 1, "SelectorTagBehavior_OnWhileCurrent": 0}, "type": "uint32"}, "SeqCmd_t": {"alignment": 4, "members": {"SeqCmd_Add": 4, "SeqCmd_Blend": 8, "SeqCmd_Copy": 7, "SeqCmd_FetchCycle": 11, "SeqCmd_FetchFrame": 12, "SeqCmd_FetchFrameRange": 2, "SeqCmd_IKLockInPlace": 13, "SeqCmd_IKRestoreAll": 14, "SeqCmd_LinearDelta": 1, "SeqCmd_Nop": 0, "SeqCmd_ReverseSequence": 15, "SeqCmd_Scale": 6, "SeqCmd_Sequence": 10, "SeqCmd_Slerp": 3, "SeqCmd_Subtract": 5, "SeqCmd_Transform": 16, "SeqCmd_Worldspace": 9}, "type": "uint32"}, "SeqPoseSetting_t": {"alignment": 4, "members": {"SEQ_POSE_SETTING_CONSTANT": 0, "SEQ_POSE_SETTING_POSITION": 2, "SEQ_POSE_SETTING_ROTATION": 1, "SEQ_POSE_SETTING_VELOCITY": 3}, "type": "uint32"}, "SolveIKChainAnimNodeDebugSetting": {"alignment": 4, "members": {"SOLVEIKCHAINANIMNODEDEBUGSETTING_Forward": 4, "SOLVEIKCHAINANIMNODEDEBUGSETTING_Left": 6, "SOLVEIKCHAINANIMNODEDEBUGSETTING_None": 0, "SOLVEIKCHAINANIMNODEDEBUGSETTING_Up": 5, "SOLVEIKCHAINANIMNODEDEBUGSETTING_X_Axis_Circle": 1, "SOLVEIKCHAINANIMNODEDEBUGSETTING_Y_Axis_Circle": 2, "SOLVEIKCHAINANIMNODEDEBUGSETTING_Z_Axis_Circle": 3}, "type": "uint32"}, "StanceOverrideMode": {"alignment": 4, "members": {"Node": 1, "Sequence": 0}, "type": "uint32"}, "StateActionBehavior": {"alignment": 4, "members": {"STATETAGBEHAVIOR_ACTIVE_WHILE_CURRENT": 0, "STATETAGBEHAVIOR_FIRE_ON_ENTER": 1, "STATETAGBEHAVIOR_FIRE_ON_ENTER_AND_EXIT": 3, "STATETAGBEHAVIOR_FIRE_ON_EXIT": 2}, "type": "uint32"}, "StepPhase": {"alignment": 4, "members": {"StepPhase_InAir": 1, "StepPhase_OnGround": 0}, "type": "uint32"}, "VPhysXAggregateData_t__VPhysXFlagEnum_t": {"alignment": 4, "members": {"FLAG_IGNORE_SCALE_OBSOLETE_DO_NOT_USE": 32, "FLAG_IS_POLYSOUP_GEOMETRY": 1, "FLAG_LEVEL_COLLISION": 16}, "type": "uint32"}, "VPhysXBodyPart_t__VPhysXFlagEnum_t": {"alignment": 4, "members": {"FLAG_ALWAYS_DYNAMIC_ON_CLIENT": 16, "FLAG_JOINT": 4, "FLAG_KINEMATIC": 2, "FLAG_MASS": 8, "FLAG_STATIC": 1}, "type": "uint32"}, "VPhysXConstraintParams_t__EnumFlags0_t": {"alignment": 4, "members": {"FLAG0_SHIFT_BREAKABLE_FORCE": 2, "FLAG0_SHIFT_BREAKABLE_TORQUE": 3, "FLAG0_SHIFT_CONSTRAIN": 1, "FLAG0_SHIFT_INTERPENETRATE": 0}, "type": "uint32"}, "VPhysXJoint_t__Flags_t": {"alignment": 4, "members": {"JOINT_FLAGS_BODY1_FIXED": 1, "JOINT_FLAGS_NONE": 0, "JOINT_FLAGS_USE_BLOCK_SOLVER": 2}, "type": "uint32"}, "VelocityMetricMode": {"alignment": 1, "members": {"DirectionAndMagnitude": 2, "DirectionOnly": 0, "MagnitudeOnly": 1}, "type": "uint8"}}}}