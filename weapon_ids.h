#pragma once

// IDs de definição de item para armas do CS2
// Útil para referência ao configurar skins

namespace WeaponIDs {
    // Lista completa de weapon IDs do CS2
    constexpr int WEAPON_DEAGLE = 1;
    constexpr int WEAPON_ELITE = 2;
    constexpr int WEAPON_FIVESEVEN = 3;
    constexpr int WEAPON_GLOCK = 4;
    constexpr int WEAPON_AK47 = 7;
    constexpr int WEAPON_AUG = 8;
    constexpr int WEAPON_AWP = 9;
    constexpr int WEAPON_FAMAS = 10;
    constexpr int WEAPON_G3SG1 = 11;
    constexpr int WEAPON_GALILAR = 13;
    constexpr int WEAPON_M249 = 14;
    constexpr int WEAPON_M4A1 = 16;
    constexpr int WEAPON_MAC10 = 17;
    constexpr int WEAPON_P90 = 19;
    constexpr int WEAPON_UMP = 24;
    constexpr int WEAPON_XM1014 = 25;
    constexpr int WEAPON_BIZON = 26;
    constexpr int WEAPON_MAG7 = 27;
    constexpr int WEAPON_NEGEV = 28;
    constexpr int WEAPON_SAWEDOFF = 29;
    constexpr int WEAPON_TEC9 = 30;
    constexpr int WEAPON_TASER = 31;
    constexpr int WEAPON_HKP2000 = 32;
    constexpr int WEAPON_MP7 = 33;
    constexpr int WEAPON_MP9 = 34;
    constexpr int WEAPON_NOVA = 35;
    constexpr int WEAPON_P250 = 36;
    constexpr int WEAPON_SCAR20 = 38;
    constexpr int WEAPON_SG556 = 39;
    constexpr int WEAPON_SSG08 = 40;
    constexpr int WEAPON_KNIFE = 42;
    constexpr int WEAPON_FLASHBANG = 43;
    constexpr int WEAPON_HEGRENADE = 44;
    constexpr int WEAPON_SMOKEGRENADE = 45;
    constexpr int WEAPON_MOLOTOV = 46;
    constexpr int WEAPON_DECOY = 47;
    constexpr int WEAPON_INCGRENADE = 48;
    constexpr int WEAPON_C4 = 49;
    constexpr int WEAPON_KNIFE_T = 59;
    constexpr int WEAPON_M4A1_SILENCER = 60;
    constexpr int WEAPON_USP_SILENCER = 61;
    constexpr int WEAPON_CZ75A = 63;
    constexpr int WEAPON_REVOLVER = 64;

    // Facas especiais
    constexpr int WEAPON_BAYONET = 500;
    constexpr int WEAPON_KNIFE_FLIP = 505;
    constexpr int WEAPON_KNIFE_GUT = 506;
    constexpr int WEAPON_KNIFE_KARAMBIT = 507;
    constexpr int WEAPON_KNIFE_M9_BAYONET = 508;
    constexpr int WEAPON_KNIFE_TACTICAL = 509;
    constexpr int WEAPON_KNIFE_FALCHION = 512;
    constexpr int WEAPON_KNIFE_SURVIVAL_BOWIE = 514;
    constexpr int WEAPON_KNIFE_BUTTERFLY = 515;
    constexpr int WEAPON_KNIFE_PUSH = 516;
    constexpr int WEAPON_KNIFE_KUKRI = 526;
}

// Paint Kit IDs populares para referência
namespace PopularSkins {
    // AK-47
    constexpr int AK47_REDLINE = 433;
    constexpr int AK47_VULCAN = 300;
    constexpr int AK47_FIRE_SERPENT = 180;
    constexpr int AK47_CASE_HARDENED = 44;
    constexpr int AK47_WASTELAND_REBEL = 381;

    // M4A4
    constexpr int M4A4_HOWL = 309;
    constexpr int M4A4_ASIIMOV = 255;
    constexpr int M4A4_DRAGON_KING = 361;
    constexpr int M4A4_DESOLATE_SPACE = 584;

    // M4A1-S
    constexpr int M4A1S_HYPER_BEAST = 619;
    constexpr int M4A1S_CYREX = 401;
    constexpr int M4A1S_GOLDEN_COIL = 631;
    constexpr int M4A1S_KNIGHT = 157;

    // AWP
    constexpr int AWP_DRAGON_LORE = 344;
    constexpr int AWP_ASIIMOV = 279;
    constexpr int AWP_LIGHTNING_STRIKE = 42;
    constexpr int AWP_HYPER_BEAST = 656;
    constexpr int AWP_MEDUSA = 425;

    // Glock
    constexpr int GLOCK_WATER_ELEMENTAL = 437;
    constexpr int GLOCK_FADE = 38;
    constexpr int GLOCK_TWILIGHT_GALAXY = 653;

    // USP-S
    constexpr int USPS_KILL_CONFIRMED = 504;
    constexpr int USPS_ORION = 313;
    constexpr int USPS_CAIMAN = 282;

    // Desert Eagle
    constexpr int DEAGLE_BLAZE = 37;
    constexpr int DEAGLE_CONSPIRACY = 334;
    constexpr int DEAGLE_HYPNOTIC = 35;

    // P250
    constexpr int P250_ASIIMOV = 125;
    constexpr int P250_WHITEOUT = 36;
    constexpr int P250_MEHNDI = 101;
}
