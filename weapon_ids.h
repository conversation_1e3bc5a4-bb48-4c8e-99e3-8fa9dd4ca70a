#pragma once

// IDs de definição de item para armas do CS2
// Útil para referência ao configurar skins

namespace WeaponIDs {
    // Pistolas
    constexpr int DEAGLE = 1;
    constexpr int ELITE = 2;
    constexpr int FIVESEVEN = 3;
    constexpr int GLOCK = 4;
    constexpr int P228 = 6;
    constexpr int USP = 61;
    constexpr int P250 = 36;
    constexpr int TEC9 = 30;
    constexpr int CZ75A = 63;
    constexpr int REVOLVER = 64;

    // Rifles
    constexpr int AK47 = 7;
    constexpr int AUG = 8;
    constexpr int AWP = 9;
    constexpr int FAMAS = 10;
    constexpr int G3SG1 = 11;
    constexpr int GALIL = 13;
    constexpr int M249 = 14;
    constexpr int M4A4 = 16;
    constexpr int M4A1 = 60;
    constexpr int SCOUT = 40;
    constexpr int SG556 = 39;
    constexpr int SCAR20 = 38;

    // SMGs
    constexpr int MAC10 = 17;
    constexpr int MP9 = 18;
    constexpr int P90 = 19;
    constexpr int UMP45 = 24;
    constexpr int BIZON = 26;
    constexpr int MP7 = 33;
    constexpr int MP5SD = 23;

    // Shotguns
    constexpr int XM1014 = 25;
    constexpr int MAG7 = 27;
    constexpr int SAWEDOFF = 29;
    constexpr int NOVA = 35;

    // Machine Guns
    constexpr int NEGEV = 28;

    // Granadas
    constexpr int HEGRENADE = 44;
    constexpr int FLASHBANG = 43;
    constexpr int SMOKEGRENADE = 45;
    constexpr int INCGRENADE = 46;
    constexpr int DECOY = 47;
    constexpr int MOLOTOV = 48;

    // Facas
    constexpr int KNIFE = 42;
    constexpr int KNIFE_T = 59;
}

// Paint Kit IDs populares para referência
namespace PopularSkins {
    // AK-47
    constexpr int AK47_REDLINE = 433;
    constexpr int AK47_VULCAN = 300;
    constexpr int AK47_FIRE_SERPENT = 180;
    constexpr int AK47_CASE_HARDENED = 44;
    constexpr int AK47_WASTELAND_REBEL = 381;

    // M4A4
    constexpr int M4A4_HOWL = 309;
    constexpr int M4A4_ASIIMOV = 255;
    constexpr int M4A4_DRAGON_KING = 361;
    constexpr int M4A4_DESOLATE_SPACE = 584;

    // M4A1-S
    constexpr int M4A1S_HYPER_BEAST = 619;
    constexpr int M4A1S_CYREX = 401;
    constexpr int M4A1S_GOLDEN_COIL = 631;
    constexpr int M4A1S_KNIGHT = 157;

    // AWP
    constexpr int AWP_DRAGON_LORE = 344;
    constexpr int AWP_ASIIMOV = 279;
    constexpr int AWP_LIGHTNING_STRIKE = 42;
    constexpr int AWP_HYPER_BEAST = 656;
    constexpr int AWP_MEDUSA = 425;

    // Glock
    constexpr int GLOCK_WATER_ELEMENTAL = 437;
    constexpr int GLOCK_FADE = 38;
    constexpr int GLOCK_TWILIGHT_GALAXY = 653;

    // USP-S
    constexpr int USPS_KILL_CONFIRMED = 504;
    constexpr int USPS_ORION = 313;
    constexpr int USPS_CAIMAN = 282;

    // Desert Eagle
    constexpr int DEAGLE_BLAZE = 37;
    constexpr int DEAGLE_CONSPIRACY = 334;
    constexpr int DEAGLE_HYPNOTIC = 35;

    // P250
    constexpr int P250_ASIIMOV = 125;
    constexpr int P250_WHITEOUT = 36;
    constexpr int P250_MEHNDI = 101;
}
