# Guia de Instalação - CS2 External Skin Changer

## 📋 Pré-requisitos

### Software Necessário
1. **Visual Studio 2019 ou superior** (Community Edition é suficiente)
   - Baixe em: https://visualstudio.microsoft.com/downloads/
   - Durante a instalação, selecione:
     - "Desktop development with C++"
     - Windows 10/11 SDK

2. **CMake 3.16 ou superior**
   - Baixe em: https://cmake.org/download/
   - Adicione ao PATH durante a instalação

3. **Git** (opcional, para clonar o repositório)
   - Baixe em: https://git-scm.com/downloads

### Sistema Operacional
- Windows 10 ou Windows 11 (64-bit)
- Privilégios de administrador

## 🚀 Instalação Rápida

### Método 1: Usando o Script de Build
1. Baixe ou clone o projeto
2. Abra o prompt de comando como **administrador**
3. Navegue até a pasta do projeto
4. Execute: `build.bat`
5. O executável será criado em `CS2_SkinChanger.exe`

### Método 2: Compilação Manual

#### Passo 1: Preparar o Ambiente
```bash
# Abrir prompt de comando na pasta do projeto
cd "caminho\para\o\projeto"

# Criar pasta de build
mkdir build
cd build
```

#### Passo 2: Configurar com CMake
```bash
# Configurar projeto para x64
cmake .. -A x64

# Ou para sistemas com Visual Studio 2022
cmake .. -G "Visual Studio 17 2022" -A x64
```

#### Passo 3: Compilar
```bash
# Compilar em modo Release
cmake --build . --config Release

# Ou compilar em modo Debug (para desenvolvimento)
cmake --build . --config Debug
```

#### Passo 4: Localizar o Executável
O executável será criado em:
- `build\bin\Release\CS2_SkinChanger.exe` (Release)
- `build\bin\Debug\CS2_SkinChanger.exe` (Debug)

## 🔧 Configuração

### Primeira Execução
1. **Execute o CS2** primeiro
2. **Execute o CS2_SkinChanger.exe como administrador**
3. Pressione Enter quando solicitado
4. Entre em uma partida e pegue as armas

### Personalização de Skins
Edite o arquivo `skins.h` para modificar as skins:

```cpp
// Exemplo: Alterar skin do AK-47
case WeaponIDs::AK47: 
    return { 300, 0.05f, 1337, 42 }; // Vulcan, Factory New, StatTrak 1337, Seed 42
```

### Parâmetros da Configuração
- **Paint Kit ID**: ID da skin (consulte weapon_ids.h)
- **Wear**: Desgaste (0.0 = Factory New, 1.0 = Battle-Scarred)
- **StatTrak**: Valor do contador StatTrak
- **Seed**: Seed para padrões da skin

## 🛠️ Solução de Problemas

### Erro: "CMake não encontrado"
**Solução:**
1. Instale o CMake
2. Adicione ao PATH do sistema
3. Reinicie o prompt de comando

### Erro: "Visual Studio não encontrado"
**Solução:**
1. Instale o Visual Studio com C++ tools
2. Ou use: `cmake .. -G "MinGW Makefiles"` (se tiver MinGW)

### Erro: "Acesso negado" ao executar
**Solução:**
1. Execute como administrador
2. Desative temporariamente o antivírus
3. Adicione exceção no Windows Defender

### Erro: "cs2.exe não encontrado"
**Solução:**
1. Certifique-se de que o CS2 está rodando
2. Verifique se o nome do processo está correto
3. Execute o skin changer após iniciar o CS2

### Skins não aparecem no jogo
**Solução:**
1. Verifique se os offsets estão atualizados
2. Reinicie o programa
3. Entre em uma nova partida
4. Pegue as armas novamente

## 📁 Estrutura de Arquivos

```
CS2_SkinChanger/
├── main.cpp              # Código principal
├── memory.h              # Manipulação de memória
├── offsets.h             # Offsets do CS2
├── skins.h               # Configurações de skins
├── weapon_ids.h          # IDs das armas
├── CMakeLists.txt        # Configuração CMake
├── build.bat             # Script de compilação
├── config.json           # Configuração (futuro)
├── README.md             # Documentação
├── INSTALL.md            # Este arquivo
└── .gitignore            # Arquivos ignorados pelo Git
```

## 🔄 Atualizações

### Quando Atualizar
- Após updates do CS2
- Quando skins não funcionam
- Para adicionar novas skins

### Como Atualizar Offsets
1. Baixe novos offsets do cs2-dumper
2. Atualize o arquivo `offsets.h`
3. Recompile o projeto

### Fontes de Offsets
- https://github.com/a2x/cs2-dumper
- Comunidades de modding do CS2

## ⚠️ Avisos Importantes

### Segurança
- **Execute sempre como administrador**
- **Use por sua própria conta e risco**
- **Pode resultar em banimento da conta**

### Antivírus
- Alguns antivírus podem detectar como falso positivo
- Adicione exceção se necessário
- Compile você mesmo para maior segurança

### Legalidade
- Apenas para fins educacionais
- Não use em partidas competitivas
- Respeite os termos de serviço da Valve

## 📞 Suporte

### Problemas Comuns
1. Verifique se seguiu todos os passos
2. Consulte a seção de solução de problemas
3. Teste com configurações diferentes

### Logs de Debug
Para ativar logs detalhados, compile em modo Debug:
```bash
cmake --build . --config Debug
```

### Comunidade
- Participe de fóruns de modding
- Compartilhe soluções com outros usuários
- Contribua com melhorias no código

---

**Lembre-se: Use com responsabilidade e por sua própria conta e risco!**
