# CS2 External Skin Changer

Um skin changer externo para Counter-Strike 2 desenvolvido em C++ com offsets atualizados.

## ⚠️ Aviso Legal

Este projeto é apenas para fins educacionais. O uso de modificações externas em jogos online pode resultar em banimento da conta. Use por sua própria conta e risco.

## 🚀 Características

- **Offsets Atualizados**: Utiliza offsets atualizados para 2025-07-08
- **Skins Populares**: Inclui configurações para skins populares de várias armas
- **Aplicação Automática**: Aplica skins automaticamente quando você pega uma arma
- **StatTrak Personalizado**: Adiciona valores de StatTrak personalizados
- **Wear Configurável**: Controla o desgaste das skins

## 🛠️ Compilação

### Pré-requisitos
- Visual Studio 2019 ou superior
- CMake 3.16 ou superior
- Windows SDK

### Usando CMake
```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### Usando Visual Studio
1. Abra o Visual Studio
2. Selecione "Open a local folder"
3. Escolha a pasta do projeto
4. Configure o CMake
5. Compile o projeto

## 📖 Como Usar

1. **Inicie o CS2** primeiro
2. **Execute o programa** como administrador
3. **Pressione Enter** para iniciar o skin changer
4. **Entre em uma partida** e pegue as armas
5. As skins serão aplicadas automaticamente

## 🎨 Skins Incluídas

### Rifles
- **AK-47**: Redline
- **M4A4**: Howl
- **M4A1-S**: Hyper Beast
- **AWP**: Dragon Lore

### Pistolas
- **Glock-18**: Water Elemental
- **USP-S**: Kill Confirmed
- **Desert Eagle**: Blaze
- **P250**: Asiimov

### SMGs
- **P90**: Asiimov
- **MP7**: Nemesis
- **UMP-45**: Primal Saber

### E muito mais...

## ⚙️ Configuração

Para adicionar ou modificar skins, edite o arquivo `skins.h`:

```cpp
case WEAPON_ID: 
    return { PAINT_KIT_ID, WEAR_VALUE, STATTRAK_VALUE, SEED_VALUE };
```

### Parâmetros:
- **WEAPON_ID**: ID da definição do item da arma
- **PAINT_KIT_ID**: ID do paint kit da skin
- **WEAR_VALUE**: Valor do desgaste (0.0 = Factory New, 1.0 = Battle-Scarred)
- **STATTRAK_VALUE**: Valor do StatTrak
- **SEED_VALUE**: Seed para padrões da skin

## 🔧 Estrutura do Projeto

```
├── main.cpp          # Arquivo principal
├── memory.h          # Classe para manipulação de memória
├── offsets.h         # Offsets atualizados do CS2
├── skins.h           # Configurações das skins
├── CMakeLists.txt    # Configuração do CMake
└── README.md         # Este arquivo
```

## 🐛 Solução de Problemas

### O programa não encontra o CS2
- Certifique-se de que o CS2 está rodando
- Execute o programa como administrador
- Verifique se o nome do processo está correto ("cs2.exe")

### Skins não aparecem
- Verifique se os offsets estão atualizados
- Tente reiniciar o programa
- Entre em uma nova partida

### Erro de acesso negado
- Execute como administrador
- Desative temporariamente o antivírus
- Adicione exceção no Windows Defender

## 📝 Notas Técnicas

- **Arquitetura**: x64 apenas
- **Compatibilidade**: Windows 10/11
- **Método**: Manipulação externa de memória
- **Detecção**: Baixo risco (externo)

## 🔄 Atualizações

Os offsets podem precisar ser atualizados após updates do CS2. Monitore:
- Atualizações do jogo
- Mudanças nos offsets
- Novos IDs de skins

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique se os offsets estão atualizados
2. Consulte a seção de solução de problemas
3. Teste com diferentes configurações

## ⚖️ Disclaimer

Este software é fornecido "como está", sem garantias. O desenvolvedor não se responsabiliza por banimentos, danos ou problemas decorrentes do uso deste software.

**Use por sua própria conta e risco!**
