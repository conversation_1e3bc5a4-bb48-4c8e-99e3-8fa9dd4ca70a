# Changelog - CS2 External Skin Changer

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

## [1.0.0] - 2025-07-08

### ✨ Adicionado
- **Skin Changer Externo Completo**: Implementação inicial do skin changer para CS2
- **Offsets Atualizados**: Offsets para CS2 atualizados em 2025-07-08
- **Skins Populares**: Configurações para skins populares de várias armas
- **Sistema de Memória**: Classe Memory para manipulação segura de memória
- **Configuração Modular**: Arquivos separados para offsets, skins e IDs de armas
- **Build System**: CMake e scripts batch para compilação fácil
- **Documentação Completa**: README, INSTALL e guias detalhados

### 🎨 Skins Incluídas
#### Rifles
- **AK-47**: Redline
- **M4A4**: Howl  
- **M4A1-S**: Hyper Beast
- **AWP**: Dragon Lore
- **Famas**: Afterimage
- **<PERSON><PERSON><PERSON> AR**: Chatterbox

#### Pistolas
- **Glock-18**: Water Elemental
- **USP-S**: Kill Confirmed
- **Desert Eagle**: Blaze
- **P250**: Asiimov

#### SMGs
- **P90**: Asiimov
- **MP7**: Nemesis
- **UMP-45**: Primal Saber

### 🔧 Características Técnicas
- **Aplicação Automática**: Skins aplicadas automaticamente ao pegar armas
- **StatTrak Personalizado**: Valores de StatTrak configuráveis
- **Wear Configurável**: Controle do desgaste das skins
- **Force Update**: Sistema para forçar atualização visual
- **ViewModel Update**: Atualização forçada do modelo da arma
- **Error Handling**: Tratamento de erros e exceções

### 📁 Estrutura do Projeto
```
├── main.cpp              # Código principal
├── memory.h              # Manipulação de memória
├── offsets.h             # Offsets do CS2
├── skins.h               # Configurações de skins
├── weapon_ids.h          # IDs das armas e skins
├── CMakeLists.txt        # Configuração CMake
├── build.bat             # Script de compilação
├── test_build.bat        # Script de teste
├── config.json           # Configuração JSON
├── README.md             # Documentação principal
├── INSTALL.md            # Guia de instalação
├── CHANGELOG.md          # Este arquivo
└── .gitignore            # Arquivos ignorados
```

### 🛡️ Segurança
- **Manipulação Externa**: Não injeta código no processo do jogo
- **Baixo Risco**: Menor chance de detecção comparado a cheats internos
- **Código Limpo**: Sem ofuscação desnecessária ou código malicioso

### 🔄 Compatibilidade
- **Windows 10/11**: Suporte completo para sistemas 64-bit
- **Visual Studio**: Compatível com VS 2019 e superior
- **CMake**: Suporte para CMake 3.16+
- **CS2**: Testado com a versão atual do Counter-Strike 2

### 📋 Requisitos
- Windows 10/11 (64-bit)
- Visual Studio 2019+ ou CMake
- Privilégios de administrador
- CS2 instalado e funcionando

### ⚠️ Avisos
- **Uso por Conta e Risco**: Pode resultar em banimento da conta
- **Fins Educacionais**: Destinado apenas para aprendizado
- **Não Comercial**: Não deve ser usado para fins comerciais
- **Respeite os ToS**: Não viole os termos de serviço da Valve

---

## Formato do Changelog

Este changelog segue o formato [Keep a Changelog](https://keepachangelog.com/pt-BR/1.0.0/),
e este projeto adere ao [Semantic Versioning](https://semver.org/lang/pt-BR/).

### Tipos de Mudanças
- **✨ Adicionado** para novas funcionalidades
- **🔄 Alterado** para mudanças em funcionalidades existentes
- **❌ Depreciado** para funcionalidades que serão removidas
- **🗑️ Removido** para funcionalidades removidas
- **🐛 Corrigido** para correções de bugs
- **🛡️ Segurança** para vulnerabilidades corrigidas

---

## Próximas Versões

### [1.1.0] - Planejado
- **Config JSON**: Sistema de configuração via arquivo JSON
- **Mais Skins**: Adição de mais skins populares
- **Interface Gráfica**: GUI simples para configuração
- **Auto-Update**: Sistema de atualização automática de offsets

### [1.2.0] - Futuro
- **Knife Skins**: Suporte para skins de facas
- **Glove Skins**: Suporte para skins de luvas
- **Profile System**: Sistema de perfis de configuração
- **Hotkeys**: Atalhos de teclado para controle

---

**Mantenha-se atualizado**: Verifique regularmente por atualizações de offsets após updates do CS2.
